# Trabajo Práctico Sistemas Distribuidos

## Grupo 20 - Integrantes:

- **Integrante 1** - [<PERSON><PERSON>](https://github.com/emiliaduzac) - Padron: 110307
- **Integrante 2** - [Germinario Agustina](https://github.com/agus-germi) - Padron: 109095
- **Integrante 3** - [<PERSON><PERSON><PERSON>](https://github.com/Brubrux) - Padron: 109713

## TP-Coffee Shop Analysis

Se solicita un sistema distribuido que analice la información de ventas en una cadena de negocios de Cafés en Malasia.
- Se cuenta con información transaccional por ventas (montos, items vendidos, etc), información de los clientes, de las tiendas y de los productos ofrecidos.
- Se debe obtener:
    1. Transacciones (Id y monto) realizadas durante 2024 y 2025 entre las 06:00 AM y las 11:00 PM con monto total mayor o igual a 75.
    2. Productos más vendidos (nombre y cant) y productos que más ganancias han generado (nombre y monto), para cada mes en 2024 y 2025.
    3. TPV (Total Payment Value) por cada semestre en 2024 y 2025, para cada sucursal, para transacciones realizadas entre las 06:00 AM y las 11:00 PM.
    4. Fecha de cumpleaños de los 3 clientes que han hecho más compras durante 2024 y 2025, para cada sucursal.
---
## Cómo levantar los servicios con Docker Compose

1. **Construir las imágenes (solo la primera vez o si hay cambios en el código/Dockerfile):**
   ```sh
   docker-compose -f docker-compose-dev.yml build
   ```
   a. Si se quiere evitar el cache durante la construcción:
   ```sh
   docker-compose -f docker-compose-dev.yml build --no-cache
    ```
2. **Levantar los servicios:**
   ```sh
   docker-compose -f docker-compose-dev.yml up
   ```
3. **Detener los servicios:**
   ```sh
   docker-compose -f docker-compose-dev.yml down
   ```

## Cómo comparar los resultados obtenidos con los esperados
> Para una query en particular

Este script recibe dos archivos CSV por parámetro —el esperado y el obtenido— y compara su contenido ignorando el orden de las filas.
Permite verificar fácilmente si los resultados generados por el sistema coinciden con los valores esperados.
```python
python3 compare_csv_equiv.py \
  --expected dataset/expected_results/<expected result> \
  --actual dataset/results/results_q1/<actual result>
```

Si los archivos son equivalentes, el script imprimirá un mensaje confirmando la coincidencia.
En caso contrario, mostrará un mensaje de diferencia junto con las líneas específicas que faltan o sobran en cada archivo, facilitando la detección de discrepancias.

> Para todas las queries

Para comparar todos los resultados de todas las queries, se puede utilizar el script `compare_all.sh`. 
Utilizando el script individual mencionado arriba, hará una comparación de todos los resultados obtenidos con todos los esperados, sin indicar ningún archivo por parámetro.
Para correr:

```./compare_all.sh```

Si es necesario otorgarle permisos: 

```chmod x+ ./compare_all.sh```

## Cómo correr los tests
>Asegurarse de tener los servicios levantados con Docker Compose antes de correr los tests.
- Para correr todos los tests del proyecto:
   ```sh
   go test ./...
   ```
- Para correr tests específicos en un paquete:
   ```sh
   go test ./path/to/package