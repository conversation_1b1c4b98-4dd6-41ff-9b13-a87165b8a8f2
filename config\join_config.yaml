---
OPERATOR_TYPE: menu_count
QUEUE_LISTEN: to_join_menu_count
LISTEN_AGG_EXCHANGE: from_aggregate_top_seller_prod
LISTEN_PROCESSOR_EXCHANGE: from_preprocessor
END_EXCHANGE: join_menu_count_end_exchange
SEND_EXCHANGE: results_exchange
ROUTE_KEY_LISTEN: join_menu_count # usa la misma route key para el exchange de preprocessor y de aggregator
ROUTE_KEY_SEND: results_to_gateway
---
OPERATOR_TYPE: menu_profit
QUEUE_LISTEN: to_join_menu_profit 
LISTEN_AGG_EXCHANGE: from_aggregate_top_profit_prod
LISTEN_PROCESSOR_EXCHANGE: from_preprocessor
END_EXCHANGE: join_menu_profit_end_exchange
SEND_EXCHANGE: results_exchange
ROUTE_KEY_LISTEN: join_menu_profit # usa la misma route key para el exchange de preprocessor y de aggregator
ROUTE_KEY_SEND: results_to_gateway
---
OPERATOR_TYPE: store
QUEUE_LISTEN: to_join_store
LISTEN_AGG_EXCHANGE: from_aggregate_tpv
LISTEN_PROCESSOR_EXCHANGE: from_preprocessor
END_EXCHANGE: join_store_end_exchange
SEND_EXCHANGE: results_exchange
ROUTE_KEY_LISTEN: join_store # usa la misma route key para el exchange de preprocessor y de aggregator
ROUTE_KEY_SEND: results_to_gateway
---
OPERATOR_TYPE: user
QUEUE_LISTEN: to_join_user
LISTEN_AGG_EXCHANGE: from_aggregate_user
LISTEN_PROCESSOR_EXCHANGE: from_preprocessor
END_EXCHANGE: join_user_end_exchange
SEND_EXCHANGE: results_exchange
ROUTE_KEY_LISTEN: join_user # usa la misma route key para el exchange de preprocessor y de aggregator
ROUTE_KEY_SEND: results_to_gateway