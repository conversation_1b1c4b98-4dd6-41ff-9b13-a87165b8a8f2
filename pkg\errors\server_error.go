package errors

type ServerError string

func (pe ServerError) Error() string {
	return string(pe)
}

// Defines possible erros for the server side
const (
	FailToReadFromSocketError ServerError = "Failed to read message"
	FailToSendToSocketError   ServerError = "Failed to send message"
	FailtToSendError          ServerError = "Failed to send message to next node"
	UnkownServerOpCodeError   ServerError = "Unkown message type"
	UnkownOperatorType        ServerError = "Unkown operator type"
	MissingEnvVarError        ServerError = "Missing environment variable"
)
