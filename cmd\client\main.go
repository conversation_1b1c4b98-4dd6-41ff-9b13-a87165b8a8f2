package main

import (
	"os"
	"strconv"

	client_controller "tp1/internal/client"

	"github.com/op/go-logging"
)

var log = logging.MustGetLogger("log")

func main() {
	serverAddress := os.Getenv("SERVER_ADDRESS")
	datasetPath := os.Getenv("DATASET_PATH")
	maxRows := os.Getenv("MAX_ROWS")
	maxRowsInt, err := strconv.Atoi(maxRows)
	if err != nil {
		log.Errorf("action: parse_max_rows | status: fail | error: %v", err)
		return
	}

	clientController, err := client_controller.NewClientController(datasetPath, serverAddress, log, maxRowsInt)
	if err != nil {
		log.Errorf("action: creat_client_controller | status: fail | error: %v", err)
		return
	}

	if err := clientController.Start(); err != nil {
		log.Errorf("action: start_client_controller | status: fail | error: %v", err)
	}
}
