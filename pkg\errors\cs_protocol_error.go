package errors

type ClientServerProtocolError string

func (pe ClientServerProtocolError) Error() string {
	return string(pe)
}

// Defines possible erros for client server protocol
const (
	InvalidMessageLenError ClientServerProtocolError = "Invalid message length"
	EmptyMessageError      ClientServerProtocolError = "Empty message"
	InvalidOpCodeError     ClientServerProtocolError = "Invalid opcode: unkown"
)
