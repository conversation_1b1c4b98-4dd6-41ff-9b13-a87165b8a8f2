#!/usr/bin/env bash
set -euo pipefail

EXPECTED_DIR="dataset/expected_results"
RESULTS_DIR="dataset/results"
PY="python3"                       
COMPARE="$PY compare_csv_equiv.py" # mismo directorio o en tu PATH

# separador (coma por defecto)
DELIM="${DELIM:-,}"               

# Mapeo esperado -> obtenido (relativo a los directorios de arriba)
# Formato: <esperado.csv>;<carpeta_result>/<archivo_obtenido.csv>
PAIRS=$(cat <<'EOF'
q1.csv;results_q1/results_1.csv
q2_1.csv;results_q2_1/results_2_1.csv
q2_2.csv;results_q2_2/results_2_2.csv
q3.csv;results_q3/results_3.csv
q4.csv;results_q4/results_4.csv
EOF
)

ok=0
fail=0

echo "================================================="
echo " Comparing AlPesto results with expected results"
echo "================================================="
echo "  Expected dir: $EXPECTED_DIR"
echo "  Results dir : $RESULTS_DIR"
echo


set +e
while IFS=';' read -r exp relact; do
  [[ -z "$exp" ]] && continue
  exp_path="$EXPECTED_DIR/$exp"
  act_path="$RESULTS_DIR/$relact"

  echo "➡️  $exp  vs  $relact"
  $COMPARE \
    --expected "$exp_path" \
    --actual   "$act_path" \
    --delimiter "$DELIM" \

  status=$?
  if [[ $status -eq 0 ]]; then
    echo "   ✅ OK"
    ((ok++))
  else
    echo "   ❌ Differences detected (exit $status)"
    ((fail++))
  fi
  echo
done <<< "$PAIRS"
set -e

echo "================================================="
if [[ $fail -eq 0 ]]; then
  echo "✅ 🏁 All results match the expected outputs!"
else
  echo "📊 Summary: $ok OK, $fail with differences."
fi
echo "================================================="
[[ $fail -eq 0 ]]