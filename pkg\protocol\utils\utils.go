package utils

import "encoding/json"

// ToBytes serializes a message struct of type T into JSON bytes
func ToBytes[T any](message T) ([]byte, error) {
	return json.Marshal(message)
}

// NewMessageFromBytes deserializes JSON bytes into a message struct of type T
func NewMessageFromBytes[T any](payload []byte) (T, error) {
	var msg T
	err := json.Unmarshal(payload, &msg)
	if err != nil {
		var zero T
		return zero, err
	}
	return msg, nil
}
