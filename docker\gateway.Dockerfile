FROM golang:1.25-alpine AS builder

WORKDIR /app

COPY go.mod go.sum ./
RUN go mod download

COPY cmd/gateway/ ./cmd/gateway/
COPY /internal/server/gateway ./internal/server/gateway
COPY /middleware ./middleware
COPY /pkg ./pkg

RUN CGO_ENABLED=0 GOOS=linux go build -o gateway ./cmd/gateway

FROM alpine:latest

RUN apk --no-cache add ca-certificates

WORKDIR /app

COPY --from=builder /app/gateway .

CMD ["./gateway"]
