#!/usr/bin/env python3
"""
compare_csv_equiv.py — Compares two CSV or TXT files and checks if their contents are equivalent.

-> Ignores the order of rows.
-> Rounds a numeric column to N decimals (optional parameter).
-> Allows setting the field delimiter (default: comma).
-> Can handle a header in one file and not in the other.
-> Prints detailed differences if any are found.

Usage:
  python compare_csv_equiv.py --expected <expected file> --actual <actual file>
"""

import argparse, csv, sys

def read_rows(path, has_header, delimiter, id_idx0, num_idx0):
    rows = []
    with open(path, newline="", encoding="utf-8") as f:
        reader = csv.reader(f, delimiter=delimiter)
        if has_header:
            next(reader, None)  # skip header
        for line in reader:
            if len(line) <= max(id_idx0, num_idx0):
                continue
            rows.append(tuple(x.strip() for x in line))
    return rows

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--expected", required=True, help="Expected file (with header)")
    parser.add_argument("--actual", required=True, help="Actual file (without header)")
    parser.add_argument("--delimiter", default=",", help="Field delimiter (default: ',')")
    args = parser.parse_args()

    try:
        exp = read_rows(args.expected, True, args.delimiter, 0, 1)
        act = read_rows(args.actual, False, args.delimiter, 0, 1)
    except FileNotFoundError as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(2)

    exp_sorted = sorted(exp)
    act_sorted = sorted(act)

    if exp_sorted == act_sorted:
        print(f"✅ Equivalent results between {args.expected} and {args.actual}.")
        sys.exit(0)
    else:
        print("❌ Differences detected:")
        exp_set, act_set = set(exp_sorted), set(act_sorted)
        only_exp = exp_set - act_set
        only_act = act_set - exp_set
        print(f"  In expected but not in actual: {len(only_exp)} rows")
        print(f"  In actual but not in expected: {len(only_act)} rows")
        if only_exp:
            print("\nMissing rows (expected):")
            for r in list(only_exp)[:5]:
                print(" -", ",".join(r))
        if only_act:
            print("\nExtra rows (actual):")
            for r in list(only_act)[:5]:
                print(" +", ",".join(r))
        sys.exit(1)

if __name__ == "__main__":
    main()
