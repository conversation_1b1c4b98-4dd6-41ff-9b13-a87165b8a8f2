package join

import (
	"fmt"
	"os"
	"strconv"
	"tp1/middleware"
	"tp1/pkg/eot_handler"
	e "tp1/pkg/errors"
	is_protocol "tp1/pkg/protocol/internal_server_protocol"
	t "tp1/pkg/protocol/table_types"

	"github.com/op/go-logging"
)

type Join<PERSON>perator struct {
	joiner *Join
	logger *logging.Logger

	clusterProducer *middleware.MessageMiddlewareExchange
	resultsProducer *middleware.MessageMiddlewareExchange

	clusterConsumer      *middleware.MessageMiddlewareExchange
	aggregatorConsumer   *middleware.MessageMiddlewareExchange
	preprocessorConsumer *middleware.MessageMiddlewareExchange

	config *JoinConfig

	clusterSize     int
	clusterRouteKey []string

	allMessagesChannel chan []byte

	eotHandler *eot_handler.EOTHandler

	finalTableType *t.TableType
}

func NewJoinOperator(joiner *Join, logger *logging.Logger, config *JoinConfig, clusterSize int, finalTableType *t.TableType) (*JoinOperator, error) {
	conn := middleware.GetConnection()
	ch, err := conn.Channel()
	if err != nil {
		return nil, err
	}
	clusterRouteKey := config.GetClusterKey()
	// produces to cluster when end of table is received
	clusterProducer, err := middleware.NewExchangeProducer(config.ClusterEndExchange, clusterRouteKey, "fanout", false, ch)
	if err != nil {
		return nil, err
	}

	// produces results to the next stage
	resultsProducer, err := middleware.NewExchangeProducer(config.SendExchange, config.GetRouteKeySend(), "direct", true, ch)
	if err != nil {
		return nil, err
	}

	// consumes from cluter when another filter node receives end of table
	clusterConsumer, err := middleware.NewExchangeConsumer(config.ClusterEndExchange, clusterRouteKey, "fanout", false, ch, "")
	if err != nil {
		return nil, err
	}
	logger.Infof("Listen from agg exchange: %v. Listen from preprocessor: %v", config.ListenFromAggregatorExchange, config.ListenFromPreprocessorExchange)
	// consumes from aggregators -> 1 table
	aggregatorConsumer, err := middleware.NewExchangeConsumer(config.ListenFromAggregatorExchange, config.GetRouteKeyListen(), "direct", true, ch, config.GetQueueListen())
	if err != nil {
		return nil, err
	}
	// consumes from preprocessor -> 1 table
	preprocessorConsumer, err := middleware.NewExchangeConsumer(config.ListenFromPreprocessorExchange, config.GetRouteKeyListen(), "direct", true, ch, config.GetQueueListen())
	if err != nil {
		return nil, err
	}

	j := &JoinOperator{
		joiner: joiner,
		logger: logger,

		clusterConsumer: clusterConsumer,
		clusterProducer: clusterProducer,

		resultsProducer:      resultsProducer,
		aggregatorConsumer:   aggregatorConsumer,
		preprocessorConsumer: preprocessorConsumer,

		config:          config,
		clusterSize:     clusterSize,
		clusterRouteKey: clusterRouteKey,

		allMessagesChannel: make(chan []byte),

		finalTableType: finalTableType,
	}

	j.eotHandler = eot_handler.NewEOTHandler(clusterSize, j.sendToNextNode, j.sendToClusterNode, logger)
	return j, nil
}

func (j *JoinOperator) Start() error {
	done := make(chan struct{})

	// Start consuming cluster messages
	go j.clusterConsumer.StartConsuming(j.clusterConsumer, j.joinNodeCallback)

	// Start consuming incoming messages from preprocessor
	go j.preprocessorConsumer.StartConsuming(j.preprocessorConsumer, j.joinNodeCallback)

	// Start consuming incoming messages from aggregators

	go j.aggregatorConsumer.StartConsuming(j.aggregatorConsumer, j.joinNodeCallback)

	j.mainChannelListener()
	<-done
	return nil
}

func (j *JoinOperator) joinNodeCallback(consumeChannel middleware.ConsumeChannel, done chan error) {
	for d := range *consumeChannel {
		d.Ack(false)
		j.allMessagesChannel <- d.Body
	}
	done <- nil
}

func (j *JoinOperator) mainChannelListener() {
	for msg := range j.allMessagesChannel {
		err := j.handleMessage(msg)
		if err != nil {
			j.logger.Errorf("Error processing message: %v", err)
			continue
		}
	}
}

func (j *JoinOperator) handleMessage(msgBytes []byte) error {
	opCode, err := is_protocol.GetServerOpCode(msgBytes)
	if err != nil {
		return err
	}

	switch opCode {
	case is_protocol.BATCH:
		batchMsg, err := is_protocol.NewServerMessage[is_protocol.BatchMessage](msgBytes)
		if err != nil {
			return err
		}
		joinedRows, err := j.joiner.Apply(batchMsg.TableType, batchMsg.Rows, batchMsg.CSVHeaders)
		if err != nil || joinedRows == nil {
			return nil
		}
		if len(joinedRows) == 0 {
			return nil
		}
		j.logger.Infof("Sending rows", joinedRows)
		j.sendInBatches(joinedRows, batchMsg.CSVHeaders, j.config.GetRouteKeySend(), batchMsg.ClientId)

	case is_protocol.END_OF_TABLE:
		endMsg, err := is_protocol.NewServerMessage[is_protocol.EndOfTableMessage](msgBytes)
		if err != nil {
			return err
		}
		j.logger.Infof("Received EOT message for table %v", endMsg.TableType.String())
		if endMsg.TableType == j.joiner.smallTableType {
			j.joiner.SetSmallTableDone()
			// avisas sin esperar confirmacion, xq no hace falta notificar al gateway
			j.notifySmallTableEnd(endMsg)
		} else {
			// j.eotHandler.Handle(opCode, msgBytes)
		}

	case is_protocol.EOT_RECEIVED:
		endMsg, err := is_protocol.NewServerMessage[is_protocol.EndOfTableMessage](msgBytes)
		if err != nil {
			return err
		}
		j.logger.Infof("Received EOT_RECEIVED message for table %v", endMsg.TableType.String())
		if endMsg.TableType == j.joiner.smallTableType {
			// no avisas a nadie, el que recibio se encarga de avisar al resto
			j.joiner.SetSmallTableDone()
		} else {
			// j.eotHandler.Handle(opCode, msgBytes)
		}

	case is_protocol.EOT_ACK:
		panic("Type of message 'EOT_ACK' not supported by join") //should not happen - bruno
	default:
		return e.UnkownServerOpCodeError
	}
	return nil
}

func (j *JoinOperator) Send(producer *middleware.MessageMiddlewareExchange, msg []byte) error {
	sendErr := producer.Send(producer, msg)
	if sendErr != 0 {
		j.logger.Error("Error sending message: ", sendErr)
		return fmt.Errorf("error sending message: %v", sendErr)
	}
	return nil
}

// sendToNextNode builds and sends a new message to the next node in the pipeline.
func (j *JoinOperator) sendToNextNode(formerRouteKeys []string, newMsg any, opCode is_protocol.ServerOpCode) error {

	result, err := is_protocol.BuildServerMessage(newMsg, opCode, j.config.GetRouteKeySend())
	if err != nil {
		return err
	}

	return j.Send(j.resultsProducer, result)
}

// sendToClusterNode builds and sends a new message to teh rest of the nodes in the cluster.
func (j *JoinOperator) sendToClusterNode(newMsg any, opCode is_protocol.ServerOpCode) error {
	result, err := is_protocol.BuildServerMessage(newMsg, opCode, j.clusterRouteKey)
	if err != nil {
		return err
	}
	return j.Send(j.clusterProducer, result)
}

func (j *JoinOperator) notifySmallTableEnd(endMsg is_protocol.EndOfTableMessage) error {
	eotReceivedMsg := is_protocol.ClusterEndOfTableMessage{
		TableType:         endMsg.TableType,
		ClientId:          endMsg.ClientId,
		OriginalRouteKeys: []string{"bruno"},
	}
	return j.sendToClusterNode(eotReceivedMsg, is_protocol.EOT_RECEIVED)
}

func (j *JoinOperator) sendInBatches(rows [][]string, CSVHeaders []string, newRouteKeys []string, clientId string) error {
	maxRowsStr := os.Getenv("MAX_ROWS")
	maxRows, _ := strconv.Atoi(maxRowsStr)

	j.logger.Infof("Start sending batches")
	for i := 0; i < len(rows); {

		iniRange := i
		endRange := min(i+maxRows, len(rows))

		batchMsg := is_protocol.BatchMessage{
			TableType:  *j.finalTableType,
			ClientId:   clientId,
			CSVHeaders: CSVHeaders,
			Rows:       rows[iniRange:endRange],
		}

		msgBytes, err := is_protocol.BuildServerMessage(batchMsg, is_protocol.BATCH, newRouteKeys)
		if err != nil {
			j.logger.Errorf("action: sendInBatches | result: fail | error: %v", err)
			return err
		}

		if err := j.Send(j.resultsProducer, msgBytes); err != nil {
			j.logger.Errorf("action: sendInBatches | result: fail | error: %v", err)
			return err
		}

		if endRange == len(rows) {
			break
		}
		i = endRange

	}
	j.logger.Infof("Start finished sending batches")
	return nil
}
