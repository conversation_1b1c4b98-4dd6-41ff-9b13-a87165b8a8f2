package handlers

import (
	"io"
	"iter"
	"net"
	e "tp1/pkg/errors"
	cs_protocol "tp1/pkg/protocol/client_server_protocol"
)

type ConnectionHandler struct {
	clientID      string
	serverAddress string
	conn          net.Conn
}

// NewConnectionHandler establishes a TCP connection to the server and performs handshake to obtain client UUID
func NewConnectionHandler(address string) (*ConnectionHandler, error) {
	// tries to connect to the address
	conn, err := net.Dial("tcp", address)
	if err != nil {
		return nil, err
	}

	// Handshake to get UUID
	msg := cs_protocol.BeginMessage{}
	if err := cs_protocol.SendMessage(conn, msg, cs_protocol.BEGIN); err != nil {
		return nil, err
	}

	opCode, msgBytes, err := cs_protocol.ReadMessage(conn)
	if err != nil {
		return nil, err
	}

	// checkear que el opcode es ACK
	if opCode != cs_protocol.ACK {
		return nil, err
	}

	ackMsg, err := cs_protocol.NewCSMessageFromBytes[cs_protocol.AckMessage](msgBytes)
	if err != nil {
		return nil, err
	}

	return &ConnectionHandler{
		clientID:      ackMsg.ClientId,
		serverAddress: address,
		conn:          conn,
	}, nil
}

// Send encapsluates the sendMessage function with the connection of the handler
func (c ConnectionHandler) Send(msg any, opCode cs_protocol.OpCode) error {
	return cs_protocol.SendMessage(c.conn, msg, opCode)
}

// GetClientId returns the UUID of the client
func (c ConnectionHandler) GetClientId() string {
	return c.clientID
}

// GetResults gets the results of the queries
func (c *ConnectionHandler) GetResults() iter.Seq2[*cs_protocol.BatchMessage, error] {

	return func(yield func(*cs_protocol.BatchMessage, error) bool) {
		for {
			opCode, msgBytes, err := cs_protocol.ReadMessage(c.conn)
			if err != nil {

				// TODO: we may need to change this later
				// Handle EOF gracefully - connection closed by server
				if err == io.EOF {
					return // Stop iteration gracefully on EOF
				}
				if !yield(nil, err) {
					return
				}
			}

			if opCode != cs_protocol.RESULTS {
				if !yield(nil, e.UnkownOpCodeReceivedError) {
					return
				}
			}

			batchMsg, err := cs_protocol.NewCSMessageFromBytes[cs_protocol.BatchMessage](msgBytes)
			if err != nil {
				if !yield(nil, err) {
					return
				}
			}
			if !yield(&batchMsg, nil) {
				return
			}
		}
	}
}
