package middleware

// import (
// 	"testing"

// 	amqp "github.com/rabbitmq/amqp091-go"
// )

// func setupExchange(t *testing.T, exchangeName, exchangeType string, routeKeys []string) (*amqp.Connection, *amqp.Channel, *MessageMiddlewareExchange) {
// 	conn, err := amqp.Dial("amqp://guest:guest@localhost:5672/")
// 	if err != nil {
// 		t.Fatalf("No se pudo conectar a RabbitMQ: %v", err)
// 	}
// 	ch, err := conn.Channel()
// 	if err != nil {
// 		t.Fatalf("No se pudo abrir el canal: %v", err)
// 	}
// 	prod, err := NewExchangeProducer(exchangeName, routeKeys, exchangeType, ch)
// 	if err != nil {
// 		t.Fatalf("Error creando productor: %v", err)
// 	}
// 	return conn, ch, prod
// }

// func newExchangeConsumer(t *testing.T, exchangeName, exchangeType string, routeKeys []string, ch *amqp.Channel) *MessageMiddlewareExchange {
// 	cons, err := NewExchangeConsumer(exchangeName, routeKeys, exchangeType, ch)
// 	if err != nil {
// 		t.Fatalf("Error creando consumidor: %v", err)
// 	}
// 	return cons
// }

// func makeExchangeCallback(received chan string) onMessageCallback {
// 	return func(deliveries ConsumeChannel, done chan error) {
// 		for d := range *deliveries {
// 			received <- string(d.Body)
// 			_ = d.Ack(false)
// 		}
// 		done <- nil
// 	}
// }

// func cleanupExchange(t *testing.T, prod *MessageMiddlewareExchange, conn *amqp.Connection, ch *amqp.Channel) {
// 	if err := prod.Delete(prod); err != 0 {
// 		t.Errorf("Error eliminando exchange: %v", err)
// 	}
// 	if err := prod.Close(prod); err != 0 {
// 		t.Errorf("Error cerrando productor: %v", err)
// 	}
// 	if err := ch.Close(); err != nil {
// 		t.Errorf("Error cerrando canal: %v", err)
// 	}
// 	if err := conn.Close(); err != nil {
// 		t.Errorf("Error cerrando conexión: %v", err)
// 	}
// }

// // func TestExchange1to1(t *testing.T) {
// // 	exchangeName := "test-exchange-1to1"
// // 	routeKey := "test.route"
// // 	conn, ch, prod := setupExchange(t, exchangeName, "direct", []string{routeKey})
// // 	defer cleanupExchange(t, prod, conn, ch)

// // 	cons := newExchangeConsumer(t, exchangeName, "direct", []string{routeKey}, ch)
// // 	received := make(chan string, 5)
// // 	cb := makeExchangeCallback(received)
// // 	cons.StartConsuming(cons, cb)

// // 	for i := 0; i < 5; i++ {

// // 		msg := internal_protocol.ServerMessage{
// // 			RouteKey: routeKey,
// // 			OpCode:   1,
// // 			Payload:  []byte("test payload"),
// // 		}
// // 		m, err := internal_protocol.Encode(msg)
// // 		if err != nil {
// // 			t.Fatalf("failed to encode message: %v", err)
// // 		}
// // 		errSend := prod.Send(prod, m)
// // 		if errSend != 0 {
// // 			t.Errorf("Error in Send: %v", errSend)
// // 		}
// // 	}
// // 	// Verificar que se reciban los 5 mensajes
// // 	for i := 0; i < 5; i++ {
// // 		select {
// // 		case msg := <-received:
// // 			t.Logf("Mensaje recibido: %s", msg)
// // 		case <-time.After(2 * time.Second):
// // 			t.Fatalf("Timeout esperando el mensaje %d", i+1)
// // 		}
// // 	}

// // }

// // func TestExchange1toN(t *testing.T) {
// // 	exchangeName := "test-exchange-1toN"
// // 	routeKey := "key1"
// // 	routeKey2 := "key2"
// // 	conn, ch, prod := setupExchange(t, exchangeName, "direct", []string{routeKey, routeKey2})
// // 	defer cleanupExchange(t, prod, conn, ch)

// // 	cons1 := newExchangeConsumer(t, exchangeName, "direct", []string{routeKey, routeKey2}, ch)
// // 	cons2 := newExchangeConsumer(t, exchangeName, "direct", []string{routeKey}, ch)

// // 	received1 := make(chan string, 5)
// // 	received2 := make(chan string, 5)

// // 	cb1 := makeExchangeCallback(received1)
// // 	cb2 := makeExchangeCallback(received2)

// // 	cons1.StartConsuming(cons1, cb1)
// // 	cons2.StartConsuming(cons2, cb2)

// // 	for i := 0; i < 5; i++ {

// // 		msg := internal_protocol.ServerMessage{
// // 			RouteKey: routeKey,
// // 			OpCode:   1,
// // 			Payload:  []byte("test payload"),
// // 		}
// // 		msg2 := internal_protocol.ServerMessage{
// // 			RouteKey: routeKey2,
// // 			OpCode:   1,
// // 			Payload:  []byte("test payload with other route key"),
// // 		}
// // 		m, err := internal_protocol.Encode(msg)
// // 		m2, err := internal_protocol.Encode(msg2)
// // 		if err != nil {
// // 			t.Fatalf("failed to encode message: %v", err)
// // 		}
// // 		errSend := prod.Send(prod, m)
// // 		errSend = prod.Send(prod, m2)
// // 		if errSend != 0 {
// // 			t.Errorf("Error in Send: %v", errSend)
// // 		}
// // 	}
// // 	for i := 0; i < 5; i++ {
// // 		select {
// // 		case msg := <-received1:
// // 			t.Logf("Consumer 1 received: %s", msg)
// // 		case <-time.After(2 * time.Second):
// // 			t.Fatalf("Timeout waiting for messahe  %d in consumer 1", i+1)
// // 		}
// // 		select {
// // 		case msg := <-received2:
// // 			t.Logf("Consumer 2 received: %s", msg)
// // 		case <-time.After(2 * time.Second):
// // 			t.Fatalf("Timeout waiting for messahe  %d in consumer 2", i+1)
// // 		}
// // 	}
// // }
