package table_types

import "fmt"

type TableType uint8

const (
	TRANSACTIONS TableType = iota
	TRANSACTION_ITEMS
	MENU
	STORES
	USERS

	RESULTS_Q1
	RESULTS_Q2_1
	RESULTS_Q2_2
	RESULTS_Q3
	RESULTS_Q4
)

func (tt TableType) Headers() ([]string, []int, error) {
	switch tt {
	case TRANSACTIONS:
		return []string{"transaction_id", "store_id", "user_id", "final_amount", "created_at"}, []int{0, 1, 4, 7, 8}, nil
	case TRANSACTION_ITEMS:
		return []string{"transaction_id", "item_id", "quantity", "unit_price", "final_amount", "created_at"}, []int{0, 1, 2, 3, 4, 5}, nil
	case MENU:
		return []string{"item_id", "item_name"}, []int{0, 1}, nil
	case STORES:
		return []string{"store_id"}, []int{0}, nil
	case USERS:
		return []string{"user_id", "birthdate"}, []int{0, 2}, nil
	default:
		return nil, nil, fmt.Errorf("unsupported table type: %d", tt)
	}
}

func (tt TableType) String() string {
	switch tt {
	case TRANSACTIONS:
		return "transactions"
	case TRANSACTION_ITEMS:
		return "transaction_items"
	case MENU:
		return "menu_items"
	case STORES:
		return "stores"
	case USERS:
		return "users"
	case RESULTS_Q1:
		return "results_1"
	case RESULTS_Q2_1:
		return "results_2_1"
	case RESULTS_Q2_2:
		return "results_2_2"
	case RESULTS_Q3:
		return "results_3"
	case RESULTS_Q4:
		return "results_4"
	default:
		panic("unsupported table type")
	}
}
