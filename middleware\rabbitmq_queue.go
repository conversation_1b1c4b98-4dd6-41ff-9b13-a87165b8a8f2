package middleware

import (
	amqp "github.com/rabbitmq/amqp091-go"
)

func NewQueueProducer(queueName string, channel MiddlewareChannel) (*MessageMiddlewareQueue, error) {
	_, err := (*channel).QueueDeclare(
		queueName, // name
		false,
		false,
		false,
		false,
		nil,
	)
	if err != nil {
		return nil, err
	}
	return &MessageMiddlewareQueue{queueName, channel, nil}, nil
}

func NewQueueConsumer(queueName string, channel MiddlewareChannel) (*MessageMiddlewareQueue, error) {
	_, err := (*channel).QueueDeclare(
		queueName, // name
		false,
		false,
		false,
		false,
		nil,
	)
	if err != nil {
		log.Infof("Queue Declare error: %v", err)
		return nil, err
	}

	deliveries, err := (*channel).Consume(queueName, "", false, false, false, false, nil)

	return &MessageMiddlewareQueue{queueName, channel, &deliveries}, nil
}

func (q *MessageMiddlewareQueue) Send(m *MessageMiddlewareQueue, message []byte) MessageMiddlewareError {
	if m.channel == nil {
		return MessageMiddlewareDisconnectedError
	}

	err := (*m.channel).Publish(
		"",          // exchange empty = direct to queue
		m.queueName, // routing key = queue name
		false,
		false,
		amqp.Publishing{
			ContentType: "application/json",
			Body:        message,
		},
	)
	if err != nil {
		return MessageMiddlewareMessageError
	}
	return 0
}

func (q *MessageMiddlewareQueue) StartConsuming(m *MessageMiddlewareQueue, onMessageCallback onMessageCallback) MessageMiddlewareError {
	if m.consumeChannel == nil {
		return MessageMiddlewareDisconnectedError
	}

	done := make(chan error)
	go onMessageCallback(m.consumeChannel, done)
	err := <-done

	if err != nil {
		return MessageMiddlewareMessageError
	}

	return 0
}

func (q *MessageMiddlewareQueue) StopConsuming(m *MessageMiddlewareQueue) MessageMiddlewareError {
	// Si nunca se arrancó a consumir, no hacemos nada
	if m.consumeChannel == nil {
		return 0
	}

	if m.channel == nil {
		return MessageMiddlewareDisconnectedError
	}

	//TODO: should it be better to have a consumer tag ?
	err := (*m.channel).Cancel("", false)
	if err != nil {
		return MessageMiddlewareMessageError
	}

	m.consumeChannel = nil
	return 0
}

func (q *MessageMiddlewareQueue) Close(m *MessageMiddlewareQueue) (error MessageMiddlewareError) {
	if m.channel == nil {
		return MessageMiddlewareDisconnectedError
	}
	err := (*m.channel).Close()
	if err != nil {
		return MessageMiddlewareCloseError
	}
	m.channel = nil
	m.consumeChannel = nil
	return 0
}

func (q *MessageMiddlewareQueue) Delete(m *MessageMiddlewareQueue) MessageMiddlewareError {
	if m.channel == nil {
		return MessageMiddlewareDisconnectedError
	}
	_, err := (*m.channel).QueueDelete(
		m.queueName,
		false,
		false,
		false,
	)
	if err != nil {
		return MessageMiddlewareDeleteError
	}
	return 0
}
