package client_server_protocol

import (
	tt "tp1/pkg/protocol/table_types"
	"tp1/pkg/protocol/utils"
)

type BeginMessage struct{}

type BatchMessage struct {
	TableType tt.TableType `json:"TableType"`
	ClientId  string       `json:"ClientId"`
	Rows      [][]string   `json:"Rows"`
}

type AckMessage struct {
	ClientId string `json:"ClientId"`
}

type EndOfTableMessage struct {
	TableType tt.TableType `json:"TableType"`
	ClientId  string       `json:"ClientId"`
}

type EndOfDatasetMessage struct {
	ClientId string `json:"ClientId"`
}

// ToBytes serializes a client server message struct of type T into JSON bytes
func CSMessageToBytes[T any](message T) ([]byte, error) {
	return utils.ToBytes(message)
}

// NewMessageFromBytes deserializes JSON bytes into a client server message struct of type T
func NewCSMessageFromBytes[T any](payload []byte) (T, error) {
	return utils.NewMessageFromBytes[T](payload)
}
