package internal_server_protocol

import (
	"bytes"
	"strings"
	e "tp1/pkg/errors"
	"tp1/pkg/protocol/utils"
)

// ToBytes serializes a server message struct of type T into bytes, including the headers (opcode and route keys).
func BuildServerMessage[T any](msg T, opCode ServerOpCode, routeKeys []string) ([]byte, error) {
	msgBytes, err := utils.ToBytes(msg)
	if err != nil {
		return nil, err
	}

	// validate route keys
	for _, rk := range routeKeys {
		if strings.Contains(rk, ",") {
			return nil, e.InvalidRouteKeyError
		}
	}

	// parse route keys
	routeKeysBytes := []byte(strings.Join(routeKeys, ","))
	if len(routeKeysBytes) > 255 {
		return nil, e.RouteKeysTooLongError
	}

	var buf bytes.Buffer
	// add opcode
	if err := buf.WriteByte(byte(opCode)); err != nil {
		return nil, err
	}

	// add route keys lenght
	if err := buf.WriteByte(uint8(len(routeKeysBytes))); err != nil {
		return nil, err
	}

	// add route keys
	if _, err := buf.Write(routeKeysBytes); err != nil {
		return nil, err
	}

	// add payload
	if _, err := buf.Write(msgBytes); err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

func GetServerOpCode(msgBytes []byte) (ServerOpCode, error) {

	// asssert minimum length
	if len(msgBytes) < 5 {
		return 0, e.MessageNotLongEnoughError
	}

	return ServerOpCode(msgBytes[OPCODE_INDEX]), nil
}

func GetRouteKeys(msgBytes []byte) ([]string, error) {
	// asssert minimum length
	if len(msgBytes) < 5 {
		return nil, e.MessageNotLongEnoughError
	}

	// read route keys length

	routeKeysLen := uint8(msgBytes[ROUTE_KEYS_LEN_INDEX])
	if routeKeysLen == 0 {
		return nil, e.MissingRouteKeysError
	}

	// read route keys
	routeKeysBytes := msgBytes[ROUTE_KEYS_INDEX : ROUTE_KEYS_INDEX+routeKeysLen]
	rtString := string(routeKeysBytes)

	routeKeys := strings.Split(rtString, ",")
	return routeKeys, nil
}

// NewServerMessage
func NewServerMessage[T any](msgBytes []byte) (T, error) {
	routeKeysLen := uint8(msgBytes[ROUTE_KEYS_LEN_INDEX])
	payload := msgBytes[ROUTE_KEYS_INDEX+routeKeysLen:]

	return utils.NewMessageFromBytes[T](payload)
}
