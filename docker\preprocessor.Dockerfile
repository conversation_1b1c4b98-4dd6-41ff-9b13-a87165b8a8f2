FROM golang:1.25-alpine AS builder

WORKDIR /app

COPY go.mod go.sum ./
RUN go mod download

COPY cmd/preprocessor/ ./cmd/preprocessor/
COPY /internal/server/preprocessor ./internal/server/preprocessor
COPY /middleware ./middleware
COPY /pkg ./pkg

RUN CGO_ENABLED=0 GOOS=linux go build -o preprocessor ./cmd/preprocessor

FROM alpine:latest

RUN apk --no-cache add ca-certificates

WORKDIR /app

COPY --from=builder /app/preprocessor .

CMD ["./preprocessor"]