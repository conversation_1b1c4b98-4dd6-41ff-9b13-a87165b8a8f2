package main

import (
	"os"
	"strconv"
	"tp1/internal/server/preprocessor"
	"tp1/middleware"

	"github.com/op/go-logging"
)

var log = logging.MustGetLogger("log")

func main() {
	middleware.Init()
	defer middleware.Close()

	path := os.Getenv("CONFIG_PATH")
	clusterSize := os.Getenv("CLUSTER_SIZE")
	clusterSizeInt, err := strconv.Atoi(clusterSize)
	if err != nil {
		log.Errorf("action: parse_cluster_size | status: fail | error: %v", err)
		return
	}

	config, err := preprocessor.NewPreprocessorConfig(path)
	if err != nil {
		log.Errorf("action: init_preprocessor | status: fail | error: %v", err)
		return
	}
	p, err := preprocessor.NewPreprocessor(log, config, clusterSizeInt)
	if err != nil {
		log.Fatalf("action: init_preprocessor | status: fail | error: %v", err)
		return
	}
	if err := p.Start(); err != nil {
		log.Fatalf("Preprocessor could not be started: %v", err)
	}
}
