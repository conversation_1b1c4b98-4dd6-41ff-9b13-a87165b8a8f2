FROM golang:1.25-alpine AS builder

WORKDIR /app

COPY go.mod go.sum ./
RUN go mod download

COPY cmd/aggregator/ ./cmd/aggregator/
COPY /internal/server/aggregator ./internal/server/aggregator
COPY /middleware ./middleware
COPY /pkg ./pkg

RUN CGO_ENABLED=0 GOOS=linux go build -o aggregator ./cmd/aggregator

FROM alpine:latest

RUN apk --no-cache add ca-certificates

WORKDIR /app

COPY --from=builder /app/aggregator .

CMD ["./aggregator"]