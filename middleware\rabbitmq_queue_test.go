package middleware

import (
	"testing"
	"time"

	amqp "github.com/rabbitmq/amqp091-go"
)

func setupRabbit(t *testing.T, queueName string) (*amqp.Connection, *amqp.Channel, *MessageMiddlewareQueue) {
	conn, err := amqp.Dial("amqp://guest:guest@localhost:5672/")
	if err != nil {
		t.Fatalf("Could not connect to RabbitMQ: %v", err)
	}
	ch, err := conn.Channel()
	if err != nil {
		t.Fatalf("Channel could not be opened: %v", err)
	}
	prod, err := NewQueueProducer(queueName, ch)
	if err != nil {
		t.Fatalf("Error creating producer: %v", err)
	}
	return conn, ch, prod
}

func newConsumer(t *testing.T, queueName string, ch *amqp.Channel) *MessageMiddlewareQueue {
	cons, err := NewQueueConsumer(queueName, ch)
	if err != nil {
		t.Fatalf("Error creating consumer: %v", err)
	}
	return cons
}

func makeCallback(received chan string) onMessageCallback {
	return func(deliveries ConsumeChannel, done chan error) {
		for d := range *deliveries {
			received <- string(d.Body)
			_ = d.Ack(false)
		}
		done <- nil
	}
}

func sendMessages(t *testing.T, prod *MessageMiddlewareQueue, msgs []string) {
	for _, m := range msgs {
		if err := prod.Send(prod, []byte(m)); err != 0 {
			t.Errorf("Error in Send: %v", err)
		}
	}
}

func cleanupQueue(t *testing.T, prod *MessageMiddlewareQueue, conn *amqp.Connection, ch *amqp.Channel) {
	if err := prod.Delete(prod); err != 0 {
		t.Errorf("Error deleting queue: %v", err)
	}
	if err := prod.Close(prod); err != 0 {
		t.Errorf("Error closing producer: %v", err)
	}
	if err := ch.Close(); err != nil {
		t.Errorf("Error closing channel: %v", err)
	}
	if err := conn.Close(); err != nil {
		t.Errorf("Error closing connection: %v", err)
	}
}

func TestQueue1to1(t *testing.T) {
	queueName := "test-queue-1to1"
	conn, ch, prod := setupRabbit(t, queueName)
	defer cleanupQueue(t, prod, conn, ch)

	cons := newConsumer(t, queueName, ch)
	received := make(chan string, 5)
	cb := makeCallback(received)
	cons.StartConsuming(cons, cb)

	msgs := []string{"hola", "grupo20", "rabbit"}
	sendMessages(t, prod, msgs)

	timeout := time.After(2 * time.Second)
	got := []string{}
	for len(got) < len(msgs) {
		select {
		case m := <-received:
			got = append(got, m)
		case <-timeout:
			t.Fatalf("Timeout waiting for messages, received: %v", got)
		}
	}
	for _, expected := range msgs {
		found := false
		for _, g := range got {
			if g == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected %q , got=%v", expected, got)
		}
	}
}

func TestQueue1toN(t *testing.T) {
	queueName := "test-queue-1toN"
	conn, ch, prod := setupRabbit(t, queueName)
	defer cleanupQueue(t, prod, conn, ch)

	cons1 := newConsumer(t, queueName, ch)
	cons2 := newConsumer(t, queueName, ch)

	received1 := make(chan string, 5)
	received2 := make(chan string, 5)

	cb1 := makeCallback(received1)
	cb2 := makeCallback(received2)

	cons1.StartConsuming(cons1, cb1)
	cons2.StartConsuming(cons2, cb2)

	msgs := []string{"m1", "m2", "m3", "m4"}
	sendMessages(t, prod, msgs)

	timeout := time.After(2 * time.Second)
	total1, total2 := 0, 0
	for total1+total2 < len(msgs) {
		select {
		case <-received1:
			total1++
		case <-received2:
			total2++
		case <-timeout:
			t.Fatalf("timeout waiting for messages, received c1=%d c2=%d", total1, total2)
		}
	}
	t.Logf("total1=%d total2=%d", total1, total2)

}
