#!/usr/bin/env python3
"""
Use: python scale_containers.py [clients] [gateways] [preprocessors] [filters] [groupers] [aggregators] [joiners]
"""

import sys
import os

def generate_docker_compose(clients=1, gateways=1, preprocessors=1, filters=3, groupers=1, aggregators=1, joiners=1):
    
    compose_content = f"""name: coffee_shop_analysis_scaled

include:
  - path:
    - docker/filters/filters.compose.yml
    - docker/groupers/groupers.compose.yml
    - docker/aggregators/aggregators.compose.yml
    - docker/joiners/joiners.compose.yml

services:
  # RABBIT MQ
  rabbitmq:
    image: rabbitmq:4-management
    ports:
      - "5672:5672"
      - "15672:15672"
    networks:
      - app-network
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    healthcheck:
      test: ["CMD-SHELL", "rabbitmq-diagnostics check_port_connectivity"]
      interval: 10s
      timeout: 5s
      retries: 10

"""

    # GATEWAYS (servers)
    for i in range(gateways):
        compose_content += f"""  gateway-{i}:
    container_name: gateway-{i}
    build:
      context: .
      dockerfile: docker/server.Dockerfile
    image: server:latest
    env_file: ./.env
    networks:
      - app-network
    depends_on:
      rabbitmq:
        condition: service_healthy

"""

    # PREPROCESSORS
    for i in range(preprocessors):
        compose_content += f"""  preprocessor-{i}:
    container_name: preprocessor-{i}
    build:
      context: .
      dockerfile: docker/preprocessor.Dockerfile
    image: preprocessor:latest
    env_file: ./.env
    environment:
      - CONFIG_PATH=/preprocessor_config.yaml
      - CLUSTER_SIZE={preprocessors}
    depends_on:
      rabbitmq:
        condition: service_healthy
    networks:
      - app-network
    volumes:
      - ./config/preprocessor_config.yaml:/preprocessor_config.yaml

"""


    compose_content += """networks:
  app-network:
    driver: bridge
    name: alpesto-g20
"""

    return compose_content

def update_filters_compose(filters):
    
    filters_content = f"""x-filter-service: &filter-service
  build:
    context: ../../
    dockerfile: docker/filters/filter.Dockerfile
  depends_on:
    rabbitmq:
      condition: service_healthy
  networks:
    - app-network
  env_file: ../../.env
  volumes:
    - ../../config/filter_config.yaml:/filter_config.yaml
   
services:
"""

    year_filters = max(1, filters // 3)
    for i in range(year_filters):
        filters_content += f"""  filter-by-year-{i}:
    <<: *filter-service
    container_name: filter-by-year-{i}
    environment:
      - FILTER_TYPE=year
      - CLUSTER_SIZE={year_filters}
      - CONFIG_PATH=/filter_config.yaml

"""

    time_filters = max(1, filters // 4)
    for i in range(time_filters):
        deps = "\n".join([f"      - filter-by-year-{j}" for j in range(year_filters)])
        filters_content += f"""  filter-by-time-{i}:
    <<: *filter-service
    container_name: filter-by-time-{i}
    environment:
      - FILTER_TYPE=time
      - CLUSTER_SIZE={time_filters}
      - CONFIG_PATH=/filter_config.yaml
    depends_on:
{deps}

"""

    amount_filters = filters
    for i in range(amount_filters):
        deps = "\n".join([f"      - filter-by-time-{j}" for j in range(time_filters)])
        filters_content += f"""  filter-by-amount-{i}:
    <<: *filter-service
    container_name: filter-by-amount-{i}
    environment:
      - FILTER_TYPE=amount
      - CLUSTER_SIZE={amount_filters}
      - CONFIG_PATH=/filter_config.yaml
    depends_on:
{deps}

"""

    return filters_content

def update_groupers_compose(groupers):
    
    groupers_content = f"""x-grouper-service: &grouper-service
  build:
    context: ../../
    dockerfile: docker/groupers/group_by.Dockerfile
  depends_on:
    rabbitmq:
      condition: service_healthy
  networks:
    - app-network
  env_file: ../../.env

services:
"""

    for i in range(groupers):
        groupers_content += f"""  group-by-year-month-product-{i}:
    <<: *grouper-service
    container_name: group-by-year-month-product-{i}
    environment:
      - GROUP_TYPE=year-month-product

"""

    for i in range(groupers):
        groupers_content += f"""  group-by-year-month-user-store-{i}:
    <<: *grouper-service
    container_name: group-by-year-month-user-store-{i}
    environment:
      - GROUP_TYPE=year-month-user-store

"""

    # Groupers por semestre-tienda
    for i in range(groupers):
        groupers_content += f"""  group-by-year-semester-store-{i}:
    <<: *grouper-service
    container_name: group-by-year-semester-store-{i}
    environment:
      - GROUP_TYPE=year-semester-store

"""

    return groupers_content

def update_aggregators_compose(aggregators):
    """Actualiza el archivo aggregators.compose.yml"""
    
    aggregators_content = f"""x-aggregator-service: &aggregator-service
  build:
    context: ../../
    dockerfile: docker/aggregators/aggregator.Dockerfile
  depends_on:
    rabbitmq:
      condition: service_healthy
  networks:
    - app-network
  env_file: ../../.env

services:
"""

    for i in range(aggregators):
        aggregators_content += f"""  aggregate-top-seller-product-{i}:
    <<: *aggregator-service
    container_name: aggregate-top-seller-product-{i}
    environment:
      - AGGREGATOR_TYPE=top_seller_product

  aggregate-top-profit-product-{i}:
    <<: *aggregator-service
    container_name: aggregate-top-profit-product-{i}
    environment:
      - AGGREGATOR_TYPE=top_profit_product

  aggregate-top-client-{i}:
    <<: *aggregator-service
    container_name: aggregate-top-client-{i}
    environment:
      - AGGREGATOR_TYPE=client

  aggregate-tpv-{i}:
    <<: *aggregator-service
    container_name: aggregate-tpv-{i}
    environment:
      - AGGREGATOR_TYPE=tpv

"""

    return aggregators_content

def update_joiners_compose(joiners):
    """Actualiza el archivo joiners.compose.yml"""
    
    joiners_content = f"""x-join-service: &join-service
  build:
    context: ../../
    dockerfile: docker/joiners/join.Dockerfile
  depends_on:
    rabbitmq:
      condition: service_healthy
  networks:
    - app-network
  env_file: ../../.env

services:
"""

    for i in range(joiners):
        joiners_content += f"""  joiner-{i}:
    <<: *join-service
    container_name: joiner-{i}

"""

    return joiners_content

def update_clients_compose(clients):


    clients_content = f"""name: coffee_shop_client_scaled

services:
"""

    for i in range(clients):
        clients_content += f"""  client-{i}:
    container_name: client-{i}
    build:
      context: ../../
      dockerfile: docker/client/client.Dockerfile
    image: client:latest
    env_file: ../../.env
    networks:
      - app-network
    volumes:
      - ./../../dataset:/dataset

"""

    clients_content += """networks:
  app-network:
    driver: bridge
    name: alpesto-g20
"""

    return clients_content

def main():
    if len(sys.argv) == 2 and sys.argv[1] in ['-h', '--help', 'help']:
        print("Use: python scale_containers.py [clients] [gateways] [preprocessors] [filters] [groupers] [aggregators] [joiners]")
        print("Example: python scale_containers.py 3 2 3 5 2 1 1")
        print("Default: 1 1 1 3 1 1 1")
        return

    # Valores por defecto
    clients = 1
    gateways = 1
    preprocessors = 1
    filters = 3
    groupers = 1
    aggregators = 1
    joiners = 1


    if len(sys.argv) > 1:
        try:
            clients = int(sys.argv[1])
            if len(sys.argv) > 2:
                gateways = int(sys.argv[2])
            if len(sys.argv) > 3:
                preprocessors = int(sys.argv[3])
            if len(sys.argv) > 4:
                filters = int(sys.argv[4])
            if len(sys.argv) > 5:
                groupers = int(sys.argv[5])
            if len(sys.argv) > 6:
                aggregators = int(sys.argv[6])
            if len(sys.argv) > 7:
                joiners = int(sys.argv[7])
        except ValueError:
            print("Input must be an int")
            return


    print(f"  Clients: {clients}")
    print(f"  Gateways: {gateways}")
    print(f"  Preprocessors: {preprocessors}")
    print(f"  Filters: {filters}")
    print(f"  Groupers: {groupers}")
    print(f"  Aggregators: {aggregators}")
    print(f"  Joiners: {joiners}")
    print()


    compose_content = generate_docker_compose(clients, gateways, preprocessors, filters, groupers, aggregators, joiners)
    with open('docker-compose-dev.yml', 'w') as f:
        f.write(compose_content)
    print("docker-compose-dev.yml done")


    os.makedirs('docker/filters', exist_ok=True)
    with open('docker/filters/filters.compose.yml', 'w') as f:
        f.write(update_filters_compose(filters))
    print("docker/filters/filters.compose.yml updated")

    os.makedirs('docker/groupers', exist_ok=True)
    with open('docker/groupers/groupers.compose.yml', 'w') as f:
        f.write(update_groupers_compose(groupers))
    print("docker/groupers/groupers.compose.yml updated")

    os.makedirs('docker/aggregators', exist_ok=True)
    with open('docker/aggregators/aggregators.compose.yml', 'w') as f:
        f.write(update_aggregators_compose(aggregators))
    print("docker/aggregators/aggregators.compose.yml updated")

    os.makedirs('docker/joiners', exist_ok=True)
    with open('docker/joiners/joiners.compose.yml', 'w') as f:
        f.write(update_joiners_compose(joiners))
    print("docker/joiners/joiners.compose.yml updated")

    os.makedirs('docker/client', exist_ok=True)
    with open('docker/client/docker-compose-dev.yml', 'w') as f:
        f.write(update_clients_compose(clients))
    print("docker/client/docker-compose-dev.yml updated")



if __name__ == '__main__':
    main()
