package main

import (
	"os"
	"strconv"
	groupby "tp1/internal/server/group_by"
	"tp1/middleware"
	op "tp1/pkg/operators"

	"github.com/op/go-logging"
)

var log = logging.MustGetLogger("log")

func InitConfig() (*groupby.Grouper, *op.OperatorConfig, string) {
	groupType := os.Getenv("GROUP_TYPE")
	path := os.Getenv("CONFIG_PATH")
	clusterSize := os.Getenv("CLUSTER_SIZE")

	grouper, err := groupby.GroupByFactory(groupType, log)
	if err != nil {
		log.Debugf("action: init_grouper | status: fail | error %v", err)
		return nil, nil, ""
	}
	config, err := op.NewOperatorConfig(groupType, path)
	if err != nil {
		log.Errorf("action: init_grouper | status: fail | error: %v", err)
		return nil, nil, ""
	}

	log.Debugf("action: init_grouper | status: success | type: %v", groupType)
	return grouper, config, clusterSize
}

func main() {
	middleware.Init()
	defer middleware.Close()

	grouper, config, clusterSize := InitConfig()
	if grouper == nil {
		return
	}

	clusterSizeInt, err := strconv.Atoi(clusterSize)
	if err != nil {
		log.Errorf("action: parse_cluster_size | status: fail | error: %v", err)
		return
	}

	operator := groupby.NewGroupOperator(grouper, log, config, clusterSizeInt)
	err = operator.Start()
	if err != nil {
		log.Errorf("action: start_gouper_node | status: failed | error: %v", err)
	}
}
