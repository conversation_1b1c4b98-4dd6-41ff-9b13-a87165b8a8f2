package preprocessor

import (
	"fmt"
	"io"
	"os"

	e "tp1/pkg/errors"

	"gopkg.in/yaml.v3"
)

type PreProcessorConfig struct {
	QueueListen        string              `yaml:"QUEUE_LISTEN"`
	SendExchange       string              `yaml:"SEND_EXCHANGE"`
	ClusterEndExchange string              `yaml:"END_EXCHANGE"`
	Routes             map[string][]string `yaml:"ROUTES"`
}

func NewPreprocessorConfig(path string) (*PreProcessorConfig, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, fmt.Errorf("fail to open %s: %w", path, err)
	}
	defer file.Close()

	var config PreProcessorConfig
	decoder := yaml.NewDecoder(file)
	err = decoder.Decode(&config)
	if err != nil && err != io.EOF {
		return nil, err
	}

	return &config, nil
}

// GetSpecificRouteKeysSend returns the route keys to send, given a route key listen
func (o *PreProcessorConfig) GetSpecificRouteKeysSend(keys []string) ([]string, error) {
	for _, key := range keys {
		if routeKeys, ok := o.Routes[key]; ok {
			return routeKeys, nil
		}
	}

	return nil, e.UnkownRouteKey
}

// GetRouteKeysSend returns all route keys to send
func (c *PreProcessorConfig) GetRouteKeysSend() []string {
	var keys []string
	for _, value := range c.Routes {
		keys = append(keys, value...)
	}
	return keys
}

func (c *PreProcessorConfig) GetClusterKey() []string {
	return []string{"cluster-preprocessor"}
}
