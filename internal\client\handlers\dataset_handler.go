package handlers

import (
	"os"
	e "tp1/pkg/errors"
	t "tp1/pkg/protocol/table_types"
)

var TABLE_NAMES map[string]t.TableType = map[string]t.TableType{
	"transactions":      t.TRANSACTIONS,
	"transaction_items": t.TRANSACTION_ITEMS,
	"menu_items":        t.<PERSON><PERSON>,
	"stores":            t.STORES,
	"users":             t.USERS,
}

type DataSetManager struct {
	Tables map[t.TableType]*TableReader
}

// NewDataSetManager creates a DataSetManager loading all tables from the given path
//
// Returns an error if any required table is missing
func NewDataSetManager(path string) (*DataSetManager, error) {

	tableReaders, err := LoadTables(path)
	if err != nil {
		return nil, err
	}
	return &DataSetManager{Tables: tableReaders}, nil
}

// LoadTables reads the entries in the path and creates a TableHandler for each directory
//
// In case there are tables missing, returns an error
func LoadTables(path string) (map[t.TableType]*TableReader, error) {
	entries, err := os.ReadDir(path)
	if err != nil {
		return nil, err
	}

	tableMap := make(map[t.TableType]*TableReader)

	// Read entries and creates a Handler for each directory (if valid)
	for _, entry := range entries {
		if entry.IsDir() {
			tableType, ok := TABLE_NAMES[entry.Name()]
			if !ok {
				continue
			}
			table, err := NewTableReader(path+"/"+entry.Name(), tableType)
			if err != nil {
				return nil, err
			}
			tableMap[tableType] = table
		}
	}

	// Check if all tables are present
	for _, tableType := range TABLE_NAMES {
		if _, ok := tableMap[tableType]; !ok {
			return nil, e.MissingTableError
		}
	}

	return tableMap, nil
}
