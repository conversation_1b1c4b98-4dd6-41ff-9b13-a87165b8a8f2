x-aggregator-service: &aggregator-service
  build:
    context: ../../
    dockerfile: docker/aggregators/aggregator.Dockerfile
  depends_on:
    rabbitmq:
      condition: service_healthy
  networks:
    - app-network
  env_file: ../../.env
  volumes:
    - ../../config/aggregator_config.yaml:/aggregator_config.yaml

services:
  aggregate_top_seller_product:
    <<: *aggregator-service
    container_name: aggregate_top_seller_product
    environment:
      - AGGREGATOR_TYPE=top_seller_product
      - CLUSTER_SIZE=1
      - CONFIG_PATH=/aggregator_config.yaml
    depends_on:
      - group_by_year_month_product-0

  aggregate_top_profit_product:
    <<: *aggregator-service
    container_name: aggregate_top_profit_product
    environment:
      - AGGREGATOR_TYPE=top_profit_product
      - CLUSTER_SIZE=1
      - CONFIG_PATH=/aggregator_config.yaml
    depends_on:
      - group_by_year_month_product-0

  aggregate_top_user:
    <<: *aggregator-service
    container_name: aggregate_top_user
    environment:
      - AGGREGATOR_TYPE=user
      - CLUSTER_SIZE=1
      - CONFIG_PATH=/aggregator_config.yaml
    depends_on:
      - group_by_year_month_user_store-0
      # - group_by_year_month_user_store-1

  aggregate_tpv:
    <<: *aggregator-service
    container_name: aggregate_tpv
    environment:
      - AGGREGATOR_TYPE=tpv
      - CLUSTER_SIZE=1
      - CONFIG_PATH=/aggregator_config.yaml
    depends_on:
      - group_by_year_semester_store-0
      # - group_by_year_semester_store-1
      # - group_by_year_semester_store-2