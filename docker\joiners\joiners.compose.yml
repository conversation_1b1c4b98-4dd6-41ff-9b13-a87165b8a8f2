
x-join-service: &join-service
  build:
    context: ../../
    dockerfile: docker/joiners/join.Dockerfile
  depends_on:
    rabbitmq:
      condition: service_healthy
  networks:
    - app-network
  env_file: ../../.env
  volumes:
    - ../../config/join_config.yaml:/join_config.yaml

services:
  join_menu_count:
    <<: *join-service
    container_name: join_menu_count
    environment:
      - JOIN_TYPE=menu_count
      - JOIN_ID=1
      - CONFIG_PATH=/join_config.yaml
      
  join_menu_profit:
    <<: *join-service
    container_name: join_menu_profit
    environment:
      - JOIN_TYPE=menu_profit
      - JOIN_ID=1
      - CONFIG_PATH=/join_config.yaml

  join_store:
    <<: *join-service
    container_name: join_store
    environment:
      - JOIN_TYPE=store
      - JOIN_ID=1
      - CONFIG_PATH=/join_config.yaml

  join_user:
    <<: *join-service
    container_name: join_user
    environment:
      - JOIN_TYPE=user
      - JOIN_ID=1
      - CONFIG_PATH=/join_config.yaml
