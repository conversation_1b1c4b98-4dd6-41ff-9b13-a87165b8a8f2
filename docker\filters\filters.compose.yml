x-filter-service: &filter-service
  build:
    context: ../../
    dockerfile: docker/filters/filter.Dockerfile
  depends_on:
    rabbitmq:
      condition: service_healthy
  networks:
    - app-network
  env_file: ../../.env
  volumes:
    - ../../config/filter_config.yaml:/filter_config.yaml
   
services:
  filter-by-year-0:
    <<: *filter-service
    container_name: filter-by-year-0
    environment:
      - FILTER_TYPE=year
      - CLUSTER_SIZE=1
      - CONFIG_PATH=/filter_config.yaml
    depends_on:
      - preprocessor-0
      # - preprocessor-1

  # filter-by-year-1:
  #   <<: *filter-service
  #   container_name: filter-by-year-1
  #   environment:
  #     - FILTER_TYPE=year
  #     - CONFIG_PATH=/filter_config.yaml
  #     - CLUSTER_SIZE=3
  #   depends_on:
  #     - preprocessor-0
  #     - preprocessor-1

  # filter-by-year-2:
  #   <<: *filter-service
  #   container_name: filter-by-year-2
  #   environment:
  #     - FILTER_TYPE=year
  #     - CLUSTER_SIZE=3
  #     - CONFIG_PATH=/filter_config.yaml
  #   depends_on:
  #     - preprocessor-0
  #     - preprocessor-1

  filter-by-time-0:
    <<: *filter-service
    container_name: filter-by-time-0
    environment:
      - FILTER_TYPE=time
      - CLUSTER_SIZE=1
      - CONFIG_PATH=/filter_config.yaml
    depends_on:
      - filter-by-year-0
  #     - filter-by-year-1
  #     - filter-by-year-2

  # filter-by-time-1:
  #   <<: *filter-service
  #   container_name: filter-by-time-1
  #   environment:
  #     - FILTER_TYPE=time
  #     - CLUSTER_SIZE=2
  #     - CONFIG_PATH=/filter_config.yaml
  #   depends_on:
  #     - filter-by-year-0
  #     - filter-by-year-1
  #     - filter-by-year-2

  filter-by-amount-0:
    <<: *filter-service
    container_name: filter-by-amount-0
    environment:
      - FILTER_TYPE=amount
      - CLUSTER_SIZE=1
      - CONFIG_PATH=/filter_config.yaml
    depends_on:
      - filter-by-time-0
  #     - filter-by-time-1
  #     - filter-by-year-2

  # filter-by-amount-1:
  #   <<: *filter-service
  #   container_name: filter-by-amount-1
  #   environment:
  #     - FILTER_TYPE=amount
  #     - CLUSTER_SIZE=4
  #     - CONFIG_PATH=/filter_config.yaml
  #   depends_on:
  #     - filter-by-time-0
  #     - filter-by-time-1
  #     - filter-by-year-2

  # filter-by-amount-2:
  #   <<: *filter-service
  #   container_name: filter-by-amount-2
  #   environment:
  #     - FILTER_TYPE=amount
  #     - CLUSTER_SIZE=4
  #     - CONFIG_PATH=/filter_config.yaml
  #   depends_on:
  #     - filter-by-time-0
  #     - filter-by-time-1
  #     - filter-by-year-2
    
  # filter-by-amount-3:
  #   <<: *filter-service
  #   container_name: filter-by-amount-3
  #   environment:
  #     - FILTER_TYPE=amount
  #     - CLUSTER_SIZE=4
  #     - CONFIG_PATH=/filter_config.yaml
  #   depends_on:
  #     - filter-by-time-0
  #     - filter-by-time-1
  #     - filter-by-year-2
