package operators

import (
	"fmt"
	"io"
	"os"

	"gopkg.in/yaml.v3"

	e "tp1/pkg/errors"
)

type OperatorConfig struct {
	OperatorType       string              `yaml:"OPERATOR_TYPE"`
	QueueListen        string              `yaml:"QUEUE_LISTEN"`
	ListenExchange     string              `yaml:"LISTEN_EXCHANGE"`
	ClusterEndExchange string              `yaml:"END_EXCHANGE"`
	SendExchange       string              `yaml:"SEND_EXCHANGE"`
	Routes             map[string][]string `yaml:"ROUTES"`
}

// NewOperatorConfig reads the operator configuration from a YAML file
func NewOperatorConfig(operatorType string, path string) (*OperatorConfig, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, fmt.Errorf("fail to open %s: %w", path, err)
	}
	defer file.Close()

	decoder := yaml.NewDecoder(file)

	for {
		var config OperatorConfig

		err := decoder.Decode(&config)
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, fmt.Errorf("fail parsing YAML document: %w", err)
		}

		// found the matching operator type
		if config.OperatorType == operatorType {
			return &config, nil
		}
	}

	return nil, fmt.Errorf("operator not found: %s", operatorType)
}

// GetSpecificRouteKeysSend returns the route keys to send, given a route key listen
func (o *OperatorConfig) GetSpecificRouteKeysSend(keys []string) ([]string, error) {
	for _, key := range keys {
		if routeKeys, ok := o.Routes[key]; ok {
			return routeKeys, nil
		}
	}

	return nil, e.UnkownRouteKey
}

func (c *OperatorConfig) GetClusterKey() []string {
	return []string{"cluster_" + c.OperatorType}
}

// GetRouteKeysSend returns all route keys to send
func (o *OperatorConfig) GetRouteKeysSend() []string {
	var keys []string
	for _, value := range o.Routes {
		keys = append(keys, value...)
	}
	return keys
}

// GetRouteKeysListen returns all route keys to listen from
func (c *OperatorConfig) GetRouteKeysListen() []string {
	var keys []string
	for key := range c.Routes {
		keys = append(keys, key)
	}
	return keys
}
