name: coffee_shop_client

services:
  client:
    container_name: client
    build:
      context: ../../
      dockerfile: docker/client/client.Dockerfile
    image: client:latest
    env_file: ../../.env
    networks:
      - app-network
    volumes:
      - ./../../dataset:/dataset

networks:
  app-network:
    driver: bridge 
    name: alpesto-g20
    # external: true # TODO: Cambiarlo por una network compartida.
