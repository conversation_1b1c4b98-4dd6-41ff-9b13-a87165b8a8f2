package aggregator

import (
	"fmt"
	"os"
	"strconv"
	"tp1/middleware"
	e "tp1/pkg/errors"
	op "tp1/pkg/operators"
	is_protocol "tp1/pkg/protocol/internal_server_protocol"

	"github.com/op/go-logging"
)

type AggregateOperator struct {
	aggregate *Aggregate
	logger    *logging.Logger
	producer  *middleware.MessageMiddlewareExchange
	consumer  *middleware.MessageMiddlewareExchange
	config    *op.OperatorConfig
	joinSize  int
}

func NewAggregateOperator(aggregate *Aggregate, logger *logging.Logger, config *op.OperatorConfig, joinSize int) *AggregateOperator {
	conn := middleware.GetConnection()
	ch, err := conn.Channel()
	if err != nil {
		return nil
	}

	consumer, err := middleware.NewExchangeConsumer(config.ListenExchange, config.GetRouteKeysListen(), "direct", true, ch, config.QueueListen)
	if err != nil {
		return nil
	}

	producer, err := middleware.NewExchangeProducer(config.SendExchange, config.GetRouteKeysSend(), "direct", true, ch)
	if err != nil {
		return nil
	}

	return &AggregateOperator{
		aggregate: aggregate,
		logger:    logger,
		producer:  producer,
		consumer:  consumer,

		config:   config,
		joinSize: joinSize,
	}
}

func (a *AggregateOperator) Start() error {
	done := make(chan struct{})
	a.consumer.StartConsuming(a.consumer, a.aggregatorNodeCallback)
	<-done
	return nil
}

func (a *AggregateOperator) aggregatorNodeCallback(consumeChannel middleware.ConsumeChannel, done chan error) {
	for d := range *consumeChannel {
		err := d.Ack(false)
		if err != nil {
			a.logger.Error("Error acknowledging message: %v", err)
		}

		err = a.handleMessage(d.Body)
		if err != nil {
			a.logger.Errorf("Error processing message: %v", err)
			continue
		}
	}
	done <- nil
}

func (a *AggregateOperator) handleMessage(msgBytes []byte) error {
	opCode, err := is_protocol.GetServerOpCode(msgBytes)
	if err != nil {
		return err
	}

	switch opCode {
	case is_protocol.BATCH:
		return a.handleBatchMessage(msgBytes)
	case is_protocol.END_OF_TABLE:
		return a.handleEndMessage(msgBytes)
	default:
		a.logger.Error("action: read_msg | status: fail | error: Unkown OpCode")
		return e.UnkownServerOpCodeError
	}
}

// handleBatchMessage handles BACTH messages, sending them to the next node in pipeline.
func (a AggregateOperator) handleBatchMessage(msgBytes []byte) error {
	batchMsg, err := is_protocol.NewServerMessage[is_protocol.BatchMessage](msgBytes)
	if err != nil {
		return err
	}

	a.aggregate.Apply(batchMsg.Rows, batchMsg.CSVHeaders)
	return nil
}

func (a *AggregateOperator) handleEndMessage(msgBytes []byte) error {
	endMsg, err := is_protocol.NewServerMessage[is_protocol.EndOfTableMessage](msgBytes)
	if err != nil {
		return err
	}
	a.logger.Infof("action: read_end_msg | status: success | detail: EndTable message")
	aggregatedRows, headers := a.aggregate.GetAggregated()
	formerRouteKeys, err := is_protocol.GetRouteKeys(msgBytes)
	if err != nil {
		return err
	}
	newRouteKeys, err := a.config.GetSpecificRouteKeysSend(formerRouteKeys)
	if err != nil {
		return err
	}

	a.logger.Infof("Sending to routeKey %s", newRouteKeys)
	if err := a.sendInBatches(aggregatedRows, headers, newRouteKeys, endMsg.ClientId); err != nil {
		return err
	}
	return a.sendToNextNode(newRouteKeys, endMsg, is_protocol.END_OF_TABLE)
}

// sendToNextNode builds and sends a new message to the next node in the pipeline.
func (a AggregateOperator) sendToNextNode(newRouteKeys []string, newMsg any, opCode is_protocol.ServerOpCode) error {
	a.logger.Infof("ENVIO EOT")
	result, err := is_protocol.BuildServerMessage(newMsg, opCode, newRouteKeys)
	if err != nil {
		return err
	}

	return a.Send(a.producer, result)
}

func (a *AggregateOperator) Send(producer *middleware.MessageMiddlewareExchange, msg []byte) error {
	sendErr := producer.Send(producer, msg)
	if sendErr != 0 {
		a.logger.Error("Error sending message: ", sendErr)
		return fmt.Errorf("error sending message: %v", sendErr)
	}
	return nil
}

func (a *AggregateOperator) sendInBatches(rows [][]string, CSVHeaders []string, newRouteKeys []string, clientId string) error {
	maxRowsStr := os.Getenv("MAX_ROWS")
	maxRows, _ := strconv.Atoi(maxRowsStr)

	a.logger.Infof("Start sending batches")
	for i := 0; i < len(rows); {

		iniRange := i
		endRange := min(i+maxRows, len(rows))

		batchMsg := is_protocol.BatchMessage{
			TableType:  a.aggregate.tableType,
			ClientId:   clientId,
			CSVHeaders: CSVHeaders,
			Rows:       rows[iniRange:endRange],
		}

		msgBytes, err := is_protocol.BuildServerMessage(batchMsg, is_protocol.BATCH, newRouteKeys)
		if err != nil {
			a.logger.Errorf("action: sendInBatches | result: fail | error: %v", err)
			return err
		}

		if err := a.Send(a.producer, msgBytes); err != nil {
			a.logger.Errorf("action: sendInBatches | result: fail | error: %v", err)
			return err
		}

		if endRange == len(rows) {
			break
		}
		i = endRange

	}
	a.logger.Infof("Start finished sending batches")
	return nil
}
