package aggregator

import (
	"os"
	"sort"
	"strings"

	"strconv"

	e "tp1/pkg/errors"
	op "tp1/pkg/operators"
	"tp1/pkg/protocol/table_types"

	"github.com/op/go-logging"
)

type KeyMapping struct {
	Name  string
	Index int
}

// Aggregate represents the configuration for an aggregation operation.
type Aggregate struct {
	// tableType represents the table the aggregator is aggregating
	tableType table_types.TableType
	// groupByKeys define the primary grouping dimensions for aggregation. For example,
	// if looking for "top 3 products per month in 2025 & 2024", this key would be <year><month>.
	groupByKeys []KeyMapping
	// subGroupKeys define secondary grouping within each primary group.
	// In the former example, this would be the product id.
	subGroupKeys []KeyMapping
	// finalColumns represent the final columns the aggregate is supposed to return, and the indexes of
	// these columns in the original rows.
	finalColumns []KeyMapping
	// aggregatedData stores the grouped and aggregated results.
	aggregatedData map[string]map[string][]string
	// top represents the amount of results to return for each group.
	top int
	// aggregateColumnIndex is the column index of the value that has to be aggregated.
	aggregateColumnIndex int
}

// ---- AGGREGATION FACTORY ----

// AggregatorFactory returns an Aggregate instance based on the given type.
func AggregatorFactory(aggregatorType string, logger *logging.Logger) (*Aggregate, int, error) {
	finalColumns := getFinalColumnsForAggregators(aggregatorType)
	nextJoinSize, err := GetNextJoinSize(aggregatorType)
	if err != nil {
		return nil, 0, err
	}
	switch aggregatorType {
	case op.AGGREGATE_USER:
		return newAggregateByUser(finalColumns), nextJoinSize, nil
	case op.AGGREGATE_TOP_SELLER_PROD:
		return newAggregateTopProduct(finalColumns, op.TOP_1, 3), nextJoinSize, nil
	case op.AGGREGATE_TOP_PROFIT_PROD:
		return newAggregateTopProduct(finalColumns, op.TOP_1, 4), nextJoinSize, nil
	case op.AGGREGATE_TPV:
		return newAggregateTpv(finalColumns), nextJoinSize, nil
	default:
		return nil, 0, e.UnkownOperatorType
	}
}

// newAggregateByUser returns an Aggregate by User
func newAggregateByUser(finalColumns []KeyMapping) *Aggregate {
	return &Aggregate{
		tableType: table_types.TRANSACTIONS,
		subGroupKeys: []KeyMapping{
			{Name: op.USER_COLUMN, Index: op.USER_INDEX},
		},
		groupByKeys: []KeyMapping{
			{Name: op.STORE_COLUMN, Index: op.STORE_INDEX},
			{Name: op.YEAR_COLUMN, Index: op.YEAR_INDEX},
		},
		aggregatedData:       map[string]map[string][]string{},
		top:                  op.TOP_3,
		finalColumns:         finalColumns,
		aggregateColumnIndex: 4,
	}
}

// newAggregateTopProduct returns an Aggregate by top seller/profit product
func newAggregateTopProduct(finalColumns []KeyMapping, top int, aggregateColumnIndex int) *Aggregate {
	return &Aggregate{
		tableType: table_types.TRANSACTION_ITEMS,
		subGroupKeys: []KeyMapping{
			{Name: op.ITEM_COLUMN, Index: op.ITEM_INDEX},
		},
		groupByKeys: []KeyMapping{
			{Name: op.YEAR_COLUMN, Index: op.YEAR_INDEX},
			{Name: op.MONTH_COLUMN, Index: op.MONTH_INDEX},
		},
		aggregatedData:       map[string]map[string][]string{},
		top:                  top,
		finalColumns:         finalColumns,
		aggregateColumnIndex: aggregateColumnIndex,
	}
}

// newAggregateTpv returns an Aggregate by TPV
func newAggregateTpv(finalColumns []KeyMapping) *Aggregate {
	return &Aggregate{
		tableType: table_types.TRANSACTIONS,
		subGroupKeys: []KeyMapping{
			{Name: op.STORE_COLUMN, Index: op.STORE_INDEX},
		},
		groupByKeys: []KeyMapping{
			{Name: op.SEMESTER_COLUMN, Index: op.SEMESTER_INDEX},
			{Name: op.YEAR_COLUMN, Index: op.YEAR_INDEX},
		},
		aggregatedData:       map[string]map[string][]string{},
		top:                  op.ALL,
		finalColumns:         finalColumns,
		aggregateColumnIndex: 3,
	}
}

// getFinalColumnsForAggregators returns the column mappings for final output
// based on the aggregation type. Defines which columns appear in results and their order.
func getFinalColumnsForAggregators(groupType string) []KeyMapping {
	switch groupType {
	case op.AGGREGATE_TOP_PROFIT_PROD:
		return []KeyMapping{
			{Name: op.ITEM_COLUMN, Index: 0},
			{Name: op.YEAR_COLUMN, Index: 1},
			{Name: op.MONTH_COLUMN, Index: 2},
			{Name: op.PROFIT_COLUMN, Index: 4},
		}
	case op.AGGREGATE_TOP_SELLER_PROD:
		return []KeyMapping{
			{Name: op.ITEM_COLUMN, Index: 0},
			{Name: op.YEAR_COLUMN, Index: 1},
			{Name: op.MONTH_COLUMN, Index: 2},
			{Name: op.COUNT_COLUMN, Index: 3},
		}
	case op.AGGREGATE_USER:
		return []KeyMapping{
			{Name: op.STORE_COLUMN, Index: 0},
			{Name: op.USER_COLUMN, Index: 1},
			{Name: op.YEAR_COLUMN, Index: 2},
			{Name: op.MONTH_COLUMN, Index: 3},
			{Name: op.COUNT_COLUMN, Index: 4},
		}
	case op.AGGREGATE_TPV:
		return []KeyMapping{
			{Name: op.STORE_COLUMN, Index: 0},
			{Name: op.YEAR_COLUMN, Index: 1},
			{Name: op.SEMESTER_COLUMN, Index: 2},
			{Name: op.TPV_COLUMN, Index: 3},
		}
	}
	return []KeyMapping{}
}

// getColumnNames returns the name of the final columnes.
func (a *Aggregate) getColumnNames() []string {
	columnNames := make([]string, len(a.finalColumns))
	for i, col := range a.finalColumns {
		columnNames[i] = col.Name
	}
	return columnNames
}

func GetNextJoinSize(aggregatorType string) (int, error) {
	var size string
	switch aggregatorType {
	case op.AGGREGATE_USER:
		size = os.Getenv("JOIN_USER_SIZE")
	case op.AGGREGATE_TOP_SELLER_PROD:
		size = os.Getenv("JOIN_MENU_SIZE")
	case op.AGGREGATE_TOP_PROFIT_PROD:
		size = os.Getenv("JOIN_MENU_SIZE")
	case op.AGGREGATE_TPV:
		size = os.Getenv("JOIN_STORE_SIZE")
	default:
		return 0, e.MissingEnvVarError
	}
	return strconv.Atoi(size)
}

// ---- AGGREGATION LOGIC ----

// Apply processes the given rows and updates the aggregated results.
func (a *Aggregate) Apply(rows [][]string, columns []string) {
	for _, row := range rows {
		outerKey := a.buildGroupKey(a.groupByKeys, row)
		innerKey := a.buildGroupKey(a.subGroupKeys, row)

		if outerMap, found := a.aggregatedData[outerKey]; found {
			if existingRow, found := outerMap[innerKey]; found {
				// if the innerKey already existed, update it's value
				a.updateAggregatedValue(existingRow, row)
			} else {
				// otherwise, add this row
				outerMap[innerKey] = a.cloneRow(row)
			}
		} else {
			a.aggregatedData[outerKey] = map[string][]string{
				innerKey: a.cloneRow(row),
			}
		}
	}
}

// updateAggregatedValue combines a new row with an existing aggregated row.
// The result is stored back in the existing row, modifying it in-place.
func (a *Aggregate) updateAggregatedValue(existingRow []string, newRow []string) {
	if a.aggregateColumnIndex >= len(existingRow) || a.aggregateColumnIndex >= len(newRow) {
		return
	}

	currentValue, err1 := strconv.ParseFloat(newRow[a.aggregateColumnIndex], 64)
	existingValue, err2 := strconv.ParseFloat(existingRow[a.aggregateColumnIndex], 64)

	if err1 != nil || err2 != nil {
		return
	}

	newValue := currentValue + existingValue
	existingRow[a.aggregateColumnIndex] = strconv.FormatFloat(newValue, 'f', 2, 64)
}

func (a *Aggregate) cloneRow(row []string) []string {
	return append([]string(nil), row...)
}

// buildGroupKey constructs a unique string key from multiple column values.
// Uses the reusable keyBuilder for efficiency. Values are separated by '|'.
func (a *Aggregate) buildGroupKey(keyMappings []KeyMapping, row []string) string {
	var stringBuilder strings.Builder

	for i, keyMapping := range keyMappings {
		if keyMapping.Index < len(row) {
			if i > 0 {
				stringBuilder.WriteByte('|')
			}
			stringBuilder.WriteString(row[keyMapping.Index])
		}
	}

	return stringBuilder.String()
}

// extractFinalColumns projects the aggregated rows to only include the desired output columns.
func (a *Aggregate) extractFinalColumns(rows [][]string) [][]string {
	result := make([][]string, 0, len(rows))

	for _, row := range rows {
		rowRes := make([]string, len(a.finalColumns))
		for i, keyMap := range a.finalColumns {
			if keyMap.Index >= 0 && keyMap.Index < len(row) {
				rowRes[i] = row[keyMap.Index]
			}
		}
		result = append(result, rowRes)
	}
	return result
}

// GetAggregated returns the result of the aggregation. This will return the top N results based on the aggregation logic.
func (a *Aggregate) GetAggregated() ([][]string, []string) {
	var aggRows [][]string

	// iterate over each outer group, to keep top N per group
	for _, innerMap := range a.aggregatedData {
		var candidates [][]string
		for _, row := range innerMap {
			candidates = append(candidates, row)
		}
		if len(candidates) == 0 {
			continue
		}

		// sort candidates by the aggregate column in descending order
		sortRowsByColumn(candidates, a.aggregateColumnIndex)

		// take top N results
		limit := len(candidates)
		if a.top != op.ALL && a.top < limit {
			limit = a.top
		}

		for i := 0; i < limit; i++ {
			aggRows = append(aggRows, candidates[i])
		}
	}

	return a.extractFinalColumns(aggRows), a.getColumnNames()
}

func sortRowsByColumn(rows [][]string, columnIndex int) {
	sort.Slice(rows, func(i, j int) bool {
		if columnIndex >= len(rows[i]) ||
			columnIndex >= len(rows[j]) {
			return false
		}

		val1, err1 := strconv.ParseFloat(rows[i][columnIndex], 64)
		val2, err2 := strconv.ParseFloat(rows[j][columnIndex], 64)

		if err1 != nil || err2 != nil {
			return false
		}

		return val1 > val2
	})
}
