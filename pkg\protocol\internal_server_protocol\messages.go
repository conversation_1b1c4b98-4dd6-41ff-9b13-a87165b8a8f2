package internal_server_protocol

import (
	tt "tp1/pkg/protocol/table_types"
)

// BatchMessage represents the batches message that flows inside the server.
type BatchMessage struct {
	TableType  tt.TableType `json:"table_type"`
	ClientId   string       `json:"ClientId"`
	CSVHeaders []string     `json:"csv_headers"`
	Rows       [][]string   `json:"rows"`
}

// RawBatchMessage represents the batch message that flows from the gateway to the inside of the server.
type RawBatchMessage struct {
	TableType tt.TableType `json:"table_type"`
	ClientId  string       `json:"ClientId"`
	Rows      [][]string   `json:"rows"`
}

// EndOfTableMessage represents the end message.
type EndOfTableMessage struct {
	TableType tt.TableType `json:"table_type"`
	ClientId  string       `json:"ClientId"`
}

// ClusterEndOfTableMessage represents the end message that flows inside the cluster to notify all nodes about the EOF.
type ClusterEndOfTableMessage struct {
	TableType         tt.TableType `json:"table_type"`
	ClientId          string       `json:"ClientId"`
	OriginalRouteKeys []string     `json:"original_route_keys"`
}
