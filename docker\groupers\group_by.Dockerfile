FROM golang:1.25-alpine AS builder

WORKDIR /app

COPY go.mod go.sum ./
RUN go mod download

# COPY only what's needed
COPY cmd/group_by ./cmd/group_by/
COPY /internal/server/group_by ./internal/server/group_by
COPY /middleware ./middleware
COPY /pkg ./pkg

RUN CGO_ENABLED=0 GOOS=linux go build -o group_by ./cmd/group_by

FROM alpine:latest

RUN apk --no-cache add ca-certificates

WORKDIR /app

COPY --from=builder /app/group_by .

CMD ["./group_by"]
