package filter

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/op/go-logging"

	op "tp1/pkg/operators"
)

const (
	MIN_YEAR   = 2024
	MAX_YEAR   = 2025
	MIN_HOUR   = 6
	MAX_HOUR   = 23
	MIN_AMOUNT = 75
)

// FilterRule defines whether a row passes the filter criteria or not, depending on the rule
type FilterRule func(row []string, keyColumnIndex int) (bool, error)

type FilterColumns func(row []string, columns []string) []string

// Filter struct that holds the filter function to be applied
type Filter struct {
	filterFunc    FilterRule
	filterColumns FilterColumns
	filterColumn  string
	logger        *logging.Logger
}

// FilterFactory creates a Filter based on the filter type
func FilterFactory(filterType string, logger *logging.Logger) *Filter {
	switch filterType {
	case op.FILTER_BY_YEAR:
		return &Filter{
			filterByYear(),
			filterColumnsDummy(),
			op.TIME_COLUMN,
			logger}
	case op.FILTER_BY_TIME:
		return &Filter{
			filterByTime(),
			filterColumnsDummy(),
			op.TIME_COLUMN,
			logger}
	case op.FILTER_BY_AMOUNT:
		return &Filter{
			filterByAmount(),
			filterColumns(),
			op.FINAL_AMOUNT_COLUMN,
			logger}
	default:
		return nil
	}
}

// Apply the filter function to a batch of data
func (f Filter) Apply(rows [][]string, columns []string) [][]string {
	var filtered [][]string
	keyColumnIndex := op.GetColumnIndex(columns, f.filterColumn)

	if keyColumnIndex == -1 {
		f.logger.Errorf("action: filter | status: fail | error: The key column index is out of range | buscando key: %v", f.filterColumn)
		return nil
	}

	for _, row := range rows {
		if ok, err := f.filterFunc(row, keyColumnIndex); ok {
			filteredByColumns := f.filterColumns(row, columns)
			filtered = append(filtered, filteredByColumns)
		} else if err != nil {
			continue
		}
	}
	return filtered
}

// Applies a filter that checks if the year is within the specified range, reading a row
func filterByYear() FilterRule {
	return func(row []string, keyColumnIndex int) (bool, error) {
		dateStr := row[keyColumnIndex]
		yearStr := strings.Split(dateStr, "-")[0]
		year, err := strconv.Atoi(yearStr)
		if err != nil {
			return false, fmt.Errorf("FilterByYearError: %d", err)
		}
		return year >= MIN_YEAR && year <= MAX_YEAR, nil
	}
}

// Applies a filter that checks if the hour is within the specified range, reading a row
func filterByTime() FilterRule {
	return func(row []string, keyColumnIndex int) (bool, error) {
		dateStr := row[keyColumnIndex]
		timeStr := strings.Split(dateStr, " ")[1]
		hourStr := strings.Split(timeStr, ":")[0]
		hour, err := strconv.Atoi(hourStr)
		if err != nil {
			return false, fmt.Errorf("FilterByTimeError: %d", err)
		}
		return hour >= MIN_HOUR && hour <= MAX_HOUR, nil
	}
}

// Applies a filter that checks if the amount is above the minimum threshold, reading a row
func filterByAmount() FilterRule {
	return func(row []string, keyColumnIndex int) (bool, error) {
		amountStr := row[keyColumnIndex]
		amount, err := strconv.ParseFloat(amountStr, 64)
		if err != nil {
			return false, fmt.Errorf("FilterByAmountError: %d", err)
		}
		return amount >= MIN_AMOUNT, nil
	}
}

func filterColumnsDummy() FilterColumns {
	return func(row []string, columns []string) []string {
		return row
	}
}

func filterColumns() FilterColumns {
	return func(row []string, columns []string) []string {
		desiredOrder := []string{"transaction_id", "final_amount"}

		var filteredRow []string
		for _, colName := range desiredOrder {
			colIndex := op.GetColumnIndex(columns, colName)
			if colIndex != -1 && colIndex < len(row) {
				filteredRow = append(filteredRow, row[colIndex])
			}
		}
		return filteredRow
	}
}
