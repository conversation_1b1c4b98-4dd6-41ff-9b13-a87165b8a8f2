#!/bin/bash
print_container_start_message() {
    local compose_file=$1
    echo ""
    echo "📦 Building and starting $compose_file containers..."
    echo ""
} 


print_container_status_message() {
    local compose_file=$1
    local status_code=$2

    if [ $status_code -eq 0 ]; then
        echo ""
        echo "✅ All Docker containers started successfully!"
        echo "Containers are running in detached mode."
        echo ""
        echo "To view logs, run:"
        echo "  Containers logs: docker-compose -f $compose_file logs -f"
        echo "  Main logs:   docker-compose -f $compose_file logs -f"
        echo ""
        echo "To stop containers, run:"
        echo "  Containers: docker-compose -f $compose_file down"
        echo "  Main:   docker-compose -f $compose_file down"
        echo "  Or use script: ./down_containers.sh"
        echo "============================================"
    else
        echo ""
        echo "❌ Error: Failed to start main application containers!"
        echo "Exit code: $status_code"
        echo "Stopping server containers due to error..."
        docker-compose -f $compose_file down
        echo "============================================"
        exit 1
    fi
} 

client_docker_file="docker/client/docker-compose-dev.yml"
server_docker_file="docker-compose-dev.yml"

echo "============================================"
echo "Starting AlPesto Docker Containers"
echo "============================================"

print_container_start_message $server_docker_file
docker-compose -f docker-compose-dev.yml up --build -d

server_docker_status_code=$?
print_container_status_message $server_docker_file $server_docker_status_code

print_container_start_message $client_docker_file
docker-compose -f $client_docker_file up --build -d

client_docker_status_code=$?
print_container_status_message $client_docker_file $client_docker_status_code


