package middleware

import (
	"fmt"
	"tp1/pkg/protocol/internal_server_protocol"

	amqp "github.com/rabbitmq/amqp091-go"
)

func NewExchangeProducer(exchangeName string, routeKeys []string, exchangeType string, durable bool, channel MiddlewareChannel) (*MessageMiddlewareExchange, error) {
	err := (*channel).ExchangeDeclare(
		exchangeName,
		exchangeType,
		durable,
		false,
		false,
		false,
		nil,
	)
	if err != nil {
		return nil, err //TODO: add more specific errors
	}
	return &MessageMiddlewareExchange{
		exchangeName: exchangeName,
		routeKeys:    routeKeys,
		amqpChannel:  channel,
	}, nil
}

func NewExchangeConsumer(exchangeName string, routeKeys []string, exchangeType string, durable bool, channel MiddlewareChannel, queueName string) (*MessageMiddlewareExchange, error) {
	err := (*channel).ExchangeDeclare(
		exchangeName,
		exchangeType,
		durable,
		false,
		false,
		false,
		nil,
	)
	if err != nil {
		return nil, err //TODO: add more specific errors
	}

	queue, err := (*channel).QueueDeclare(
		queueName,
		durable,
		false,
		false,
		false,
		nil,
	)
	if err != nil {
		return nil, err
	}

	for _, key := range routeKeys {
		err = (*channel).QueueBind(
			queue.Name,
			key,
			exchangeName,
			false,
			nil,
		)
		if err != nil {
			return nil, err
		}
	}

	deliveries, err := (*channel).Consume(queue.Name, "", false, false, false, false, nil)
	if err != nil {
		return nil, err
	}

	return &MessageMiddlewareExchange{
		exchangeName:   exchangeName,
		routeKeys:      routeKeys,
		amqpChannel:    channel,
		consumeChannel: &deliveries,
	}, nil
}

func (e *MessageMiddlewareExchange) Send(m *MessageMiddlewareExchange, message []byte) MessageMiddlewareError {
	if m.amqpChannel == nil {
		return MessageMiddlewareDisconnectedError
	}

	routeKeyArray, err := internal_server_protocol.GetRouteKeys(message)
	if err != nil {
		fmt.Printf("Error getting route keys: %v\n", err)
		return MessageMiddlewareMessageError
	}

	for _, routeKey := range routeKeyArray {
		if !isValidRouteKey(routeKey, m.routeKeys) {
			return MessageMiddlewareMessageError //TODO: should be a more specific error
		}
		err := (*m.amqpChannel).Publish(
			m.exchangeName,
			routeKey,
			false,
			false,
			amqp.Publishing{
				ContentType: "application/json",
				Body:        message,
			},
		)
		if err != nil {
			return MessageMiddlewareMessageError
		}
	}
	return 0
}
func (e *MessageMiddlewareExchange) StopConsuming(m *MessageMiddlewareExchange) MessageMiddlewareError {
	// Si nunca se arrancó a consumir, no hacemos nada
	if m.consumeChannel == nil {
		return 0
	}

	if m.amqpChannel == nil {
		return MessageMiddlewareDisconnectedError
	}

	//TODO: should it be better to have a consumer tag ?
	err := (*m.amqpChannel).Cancel("", false)
	if err != nil {
		return MessageMiddlewareMessageError
	}

	m.consumeChannel = nil
	return 0
}

func (e *MessageMiddlewareExchange) StartConsuming(m *MessageMiddlewareExchange, onMessageCallback onMessageCallback) MessageMiddlewareError {
	if m.consumeChannel == nil {
		return MessageMiddlewareDisconnectedError
	}
	go func() {
		done := make(chan error)
		go onMessageCallback(m.consumeChannel, done)
	}()
	return 0
}

func (e *MessageMiddlewareExchange) Close(m *MessageMiddlewareExchange) MessageMiddlewareError {
	if m.amqpChannel == nil {
		return MessageMiddlewareDisconnectedError
	}
	err := (*m.amqpChannel).Close()
	if err != nil {
		return MessageMiddlewareCloseError
	}
	m.amqpChannel = nil
	m.consumeChannel = nil
	return 0
}

func (e *MessageMiddlewareExchange) Delete(m *MessageMiddlewareExchange) MessageMiddlewareError {
	if m.amqpChannel == nil {
		return MessageMiddlewareDisconnectedError
	}
	err := (*m.amqpChannel).ExchangeDelete(
		m.exchangeName,
		false,
		false,
	)
	if err != nil {
		return MessageMiddlewareDeleteError
	}
	return 0
}

func isValidRouteKey(routeKey string, routeKeys []string) bool {
	for _, key := range routeKeys {
		if key == routeKey {
			return true
		}
	}
	return false
}
