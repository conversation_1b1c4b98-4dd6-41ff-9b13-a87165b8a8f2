package join

import (
	"fmt"
	"io"
	"os"

	"gopkg.in/yaml.v3"
)

type JoinConfig struct {
	OperatorType                   string `yaml:"OPERATOR_TYPE"`
	QueueListen                    string `yaml:"QUEUE_LISTEN"`
	ListenFromAggregatorExchange   string `yaml:"LISTEN_AGG_EXCHANGE"`
	ListenFromPreprocessorExchange string `yaml:"LISTEN_PROCESSOR_EXCHANGE"`
	ClusterEndExchange             string `yaml:"END_EXCHANGE"`
	SendExchange                   string `yaml:"SEND_EXCHANGE"`
	RouteKeyListen                 string `yaml:"ROUTE_KEY_LISTEN"`
	RouteKeySend                   string `yaml:"ROUTE_KEY_SEND"`

	joinId int
}

// NewJoinConfig reads the operator configuration from a YAML file
func NewJoinConfig(operatorType string, path string, joinId int) (*JoinConfig, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, fmt.Errorf("fail to open %s: %w", path, err)
	}
	defer file.Close()

	decoder := yaml.NewDecoder(file)

	for {
		var config JoinConfig

		err := decoder.Decode(&config)
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, fmt.Errorf("fail parsing YAML document: %w", err)
		}

		// found the matching operator type
		if config.OperatorType == operatorType {
			config.joinId = joinId
			return &config, nil
		}
	}

	return nil, fmt.Errorf("operator not found: %s", operatorType)
}

func (c *JoinConfig) GetClusterKey() []string {
	return []string{"cluster_" + c.OperatorType}
}

func (c *JoinConfig) GetQueueListen() string {
	// c.QueueListen + "_" + fmt.Sprint(c.joinId)
	return c.QueueListen
}

// GetRouteKeysSend returns all route keys to send
func (c *JoinConfig) GetRouteKeySend() []string {
	return []string{c.RouteKeySend}
}

// GetRouteKeysListen returns all route keys to listen from
func (c *JoinConfig) GetRouteKeyListen() []string {
	return []string{c.RouteKeyListen}
}
