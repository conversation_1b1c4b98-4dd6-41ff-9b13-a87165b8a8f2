FROM golang:1.25-alpine AS builder

WORKDIR /app

COPY go.mod go.sum ./
RUN go mod download

COPY cmd/filter/ ./cmd/filter/
COPY /internal/server/filter ./internal/server/filter
COPY /middleware ./middleware
COPY /pkg ./pkg

RUN CGO_ENABLED=0 GOOS=linux go build -o filter ./cmd/filter

FROM alpine:latest

RUN apk --no-cache add ca-certificates

WORKDIR /app

COPY --from=builder /app/filter .

CMD ["./filter"]
