package main

import (
	"os"
	a "tp1/internal/server/aggregator"
	"tp1/middleware"
	op "tp1/pkg/operators"

	"github.com/op/go-logging"
)

var log = logging.MustGetLogger("log")

func InitConfig() (*a.Aggregate, *op.OperatorConfig, int) {
	aggregatorType := os.Getenv("AGGREGATOR_TYPE")
	path := os.Getenv("CONFIG_PATH")

	aggregator, joinSize, err := a.AggregatorFactory(aggregatorType, log)
	if err != nil {
		log.Errorf("action: init_aggregator | status: fail | error: %v", err)
		return nil, nil, 0
	}
	config, err := op.NewOperatorConfig(aggregatorType, path)
	if err != nil {
		log.Errorf("action: init_aggregator | status: fail | error: %v", err)
		return nil, nil, 0
	}

	log.Infof("action: init_aggregator | status: ok | aggregator_type: %s", aggregatorType)
	return aggregator, config, joinSize
}

func main() {
	middleware.Init()
	defer middleware.Close()

	aggregator, config, joinSize := InitConfig()
	if config == nil {
		return
	}

	aggregatorNode := a.NewAggregateOperator(aggregator, log, config, joinSize)
	err := aggregatorNode.Start()
	if err != nil {
		log.Errorf("action: start_gouper_node | status: failed | error: %v", err)
	}
}
