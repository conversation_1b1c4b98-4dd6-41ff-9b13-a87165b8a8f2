package main

import (
	"os"
	"strconv"
	join "tp1/internal/server/join"
	"tp1/middleware"
	t "tp1/pkg/protocol/table_types"

	"github.com/op/go-logging"
)

var log = logging.MustGetLogger("log")

func InitConfig() (*join.Join, *join.JoinConfig, int, *t.TableType) {
	joinType := os.Getenv("JOIN_TYPE")
	joinId := os.Getenv("JOIN_ID")
	config_path := os.Getenv("CONFIG_PATH")
	joinIdInt, err := strconv.Atoi(joinId)
	if err != nil {
		log.Errorf("action: parse_join_id | status: fail | error: %v", err)
		return nil, nil, 0, nil
	}

	joiner, clusterSize, finalTableType, err := join.JoinFactory(joinType)
	if err != nil {
		log.Errorf("action: join_factory | status: fail | error: invalid join type %v or id %v", joinType, joinId)
		return nil, nil, 0, nil
	}
	config, err := join.NewJoinConfig(joinType, config_path, joinIdInt)
	if err != nil {
		log.Errorf("action: join_factory | status: fail | error: %v", err)
		return nil, nil, 0, nil
	}

	log.Infof("action: join_factory | status: success | join_type: %v | join id: %v", joinType, joinId)
	return joiner, config, clusterSize, finalTableType
}

func main() {
	middleware.Init()
	defer middleware.Close()

	joiner, config, clusterSize, finalTableType := InitConfig()
	if joiner == nil || config == nil || clusterSize == 0 {
		return
	}

	joinNode, err := join.NewJoinOperator(joiner, log, config, clusterSize, finalTableType)
	if err != nil {
		log.Errorf("action: new_join_node | status: fail | error: %v", err)
		return
	}
	err = joinNode.Start()
	if err != nil {
		log.Errorf("%v", err)
	}
}
