package groupby

import (
	"fmt"
	"tp1/middleware"
	"tp1/pkg/eot_handler"
	op "tp1/pkg/operators"

	"github.com/op/go-logging"

	is_protocol "tp1/pkg/protocol/internal_server_protocol"
)

// Grouper operator that receives batches of rows and applies the grouping logic, sending them to the next operator.
type GroupOperator struct {
	grouper *Grouper
	logger  *logging.Logger

	producer        *middleware.MessageMiddlewareExchange
	clusterProducer *middleware.MessageMiddlewareExchange
	consumer        *middleware.MessageMiddlewareExchange
	clusterConsumer *middleware.MessageMiddlewareExchange

	config             *op.OperatorConfig
	allMessagesChannel chan []byte

	eotHandler *eot_handler.EOTHandler

	clusterSize     int
	clusterRouteKey []string
}

// NewGroupOperator creates a new GroupOperator with the given Grouper, logger, and route keys.
func NewGroupOperator(grouper *Grouper, logger *logging.Logger, config *op.OperatorConfig, clusterSize int) *GroupOperator {
	conn := middleware.GetConnection()
	ch, err := conn.Channel()
	if err != nil {
		return nil
	}
	clusterRouteKey := config.GetClusterKey()
	logger.Infof("Mi cluster route key es: ", clusterRouteKey)

	// consumes from pre processor -> both batches & end of table
	consumer, err := middleware.NewExchangeConsumer(config.ListenExchange, config.GetRouteKeysListen(), "direct", true, ch, config.QueueListen)
	if err != nil {
		return nil
	}
	// produces to next node -> both batches & end of table
	producer, err := middleware.NewExchangeProducer(config.SendExchange, config.GetRouteKeysSend(), "direct", true, ch)
	if err != nil {
		return nil
	}
	// consumes from cluter when another filter node receives end of table
	clusterConsumer, err := middleware.NewExchangeConsumer(config.ClusterEndExchange, clusterRouteKey, "fanout", false, ch, "")
	if err != nil {
		return nil
	}
	// produces to cluster when end of table is received
	clusterProducer, err := middleware.NewExchangeProducer(config.ClusterEndExchange, clusterRouteKey, "fanout", false, ch)
	if err != nil {
		return nil
	}

	g := &GroupOperator{
		grouper:            grouper,
		logger:             logger,
		producer:           producer,
		consumer:           consumer,
		clusterProducer:    clusterProducer,
		clusterConsumer:    clusterConsumer,
		config:             config,
		allMessagesChannel: make(chan []byte),
		clusterSize:        clusterSize,
		clusterRouteKey:    clusterRouteKey,
	}
	g.eotHandler = eot_handler.NewEOTHandler(clusterSize, g.sendToNextNode, g.sendToClusterNode, logger)
	return g
}

func (g *GroupOperator) Start() error {
	done := make(chan struct{})

	// Start consuming cluster messages
	go g.clusterConsumer.StartConsuming(g.clusterConsumer, g.groupNodeCallback)

	// Start consuming incoming messages
	go g.consumer.StartConsuming(g.consumer, g.groupNodeCallback)

	g.mainChannelListener()
	<-done
	return nil
}

func (g *GroupOperator) groupNodeCallback(consumeChannel middleware.ConsumeChannel, done chan error) {
	for d := range *consumeChannel {
		d.Ack(false)
		g.allMessagesChannel <- d.Body
	}
	done <- nil
}

func (g *GroupOperator) mainChannelListener() {
	for msg := range g.allMessagesChannel {
		err := g.handleMessage(msg)
		if err != nil {
			g.logger.Errorf("Error processing message: %v", err)
			continue
		}
	}
}

func (g *GroupOperator) Send(producer *middleware.MessageMiddlewareExchange, msg []byte) error {
	sendErr := producer.Send(producer, msg)
	if sendErr != 0 {
		g.logger.Error("Error sending message: ", sendErr)
		return fmt.Errorf("error sending message: %v", sendErr)
	}
	return nil
}

// processMessage processes an incoming ServerMessage based on its OpCode.
func (g *GroupOperator) handleMessage(msgBytes []byte) error {
	opCode, err := is_protocol.GetServerOpCode(msgBytes)
	if err != nil {
		return err
	}
	switch opCode {
	case is_protocol.BATCH: // BatchMessage
		return g.handleBatchMessage(msgBytes)
	case is_protocol.END_OF_TABLE, is_protocol.EOT_RECEIVED, is_protocol.EOT_ACK: // EndMessage
		g.logger.Infof("Received EOT message of size %d", len(msgBytes))
		return g.eotHandler.Handle(opCode, msgBytes)
	default: // Unkown OpCode
		return fmt.Errorf("unknown opcode: %d", opCode)
	}
}

// sendToNextNode builds and sends a new message to the next node in the pipeline.
func (g *GroupOperator) sendToNextNode(formerRouteKeys []string, newMsg any, opCode is_protocol.ServerOpCode) error {
	newRouteKeys, err := g.config.GetSpecificRouteKeysSend(formerRouteKeys)
	if err != nil {
		return err
	}
	result, err := is_protocol.BuildServerMessage(newMsg, opCode, newRouteKeys)
	if err != nil {
		return err
	}

	return g.Send(g.producer, result)
}

// sendToClusterNode builds and sends a new message to teh rest of the nodes in the cluster.
func (g *GroupOperator) sendToClusterNode(newMsg any, opCode is_protocol.ServerOpCode) error {
	result, err := is_protocol.BuildServerMessage(newMsg, opCode, g.clusterRouteKey)
	if err != nil {
		return err
	}
	return g.Send(g.clusterProducer, result)
}

func (g *GroupOperator) handleBatchMessage(msgBytes []byte) error {
	batchMsg, err := is_protocol.NewServerMessage[is_protocol.BatchMessage](msgBytes)
	if err != nil {
		return err
	}

	columns, grouped := g.grouper.Apply(batchMsg.Rows, batchMsg.CSVHeaders, g.logger)

	resMsg := is_protocol.BatchMessage{
		TableType:  batchMsg.TableType,
		ClientId:   batchMsg.ClientId,
		CSVHeaders: columns,
		Rows:       grouped,
	}

	formerRouteKeys, err := is_protocol.GetRouteKeys(msgBytes)
	if err != nil {
		return nil
	}
	return g.sendToNextNode(formerRouteKeys, resMsg, is_protocol.BATCH)
}
