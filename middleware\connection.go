package middleware

import (
	"os"
	"time"

	"github.com/op/go-logging"
	amqp "github.com/rabbitmq/amqp091-go"
)

var conn *amqp.Connection
var log = logging.MustGetLogger("log")

func Init() {
	url := os.Getenv("RABBITMQ_URL")
	var err error

	for i := 1; i <= 10; i++ {
		conn, err = amqp.Dial(url)
		if err == nil {
			log.Infof("Connected to RabbitMQ on attempt %d", i)
			return
		}
		log.Errorf("Could not connect to RabbitMQ (attempt %d/10): %v", i, err)
		time.Sleep(5 * time.Second)
	}

	log.Fatalf("Failed to connect to RabbitMQ after 10 attempts: %v", err)
}

func GetConnection() *amqp.Connection {
	return conn
}

func Close() {
	if conn != nil {
		_ = conn.Close()
	}
}
