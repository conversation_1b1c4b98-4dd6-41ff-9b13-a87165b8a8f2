package main

import (
	"os"
	"strconv"
	"tp1/internal/server/filter"
	"tp1/middleware"
	op "tp1/pkg/operators"

	"github.com/op/go-logging"
)

var log = logging.MustGetLogger("log")

func InitConfig() (*filter.Filter, *op.OperatorConfig, string) {
	filterType := os.Getenv("FILTER_TYPE")
	path := os.Getenv("CONFIG_PATH")
	clusterSize := os.Getenv("CLUSTER_SIZE")

	filterFunc := filter.FilterFactory(filterType, log)
	if filterFunc == nil {
		log.Errorf("action: init_filter | status: fail | error: Invalid filter type: %s", filterType)
		return nil, nil, ""
	}

	config, err := op.NewOperatorConfig(filterType, path)
	if err != nil {
		log.Errorf("action: configuration | status: fail | error: %v", err)
		return nil, nil, ""
	}

	log.Infof("action: init_filter | status: success | filter_type: %s", filterType)
	return filterFunc, config, clusterSize
}

func main() {
	middleware.Init()
	defer middleware.Close()

	filterRule, config, clusterSize := InitConfig()
	if filterRule == nil {
		return
	}

	clusterSizeInt, err := strconv.Atoi(clusterSize)
	if err != nil {
		log.Errorf("action: parse_cluster_size | status: fail | error: %v", err)
		return
	}

	filterNode := filter.NewFilterOperator(filterRule, log, config, clusterSizeInt)
	err = filterNode.Start()
	if err != nil {
		log.Errorf("action: start_filter_node | status: fail | error: %v", err)
	}
}
