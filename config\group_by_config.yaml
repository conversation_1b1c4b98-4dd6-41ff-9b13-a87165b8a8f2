---
OPERATOR_TYPE: year_month_product
QUEUE_LISTEN: to_groupBy_ymp_queue # cluster
LISTEN_EXCHANGE: from_filter_year
END_EXCHANGE: ymp_end_exchange # cluster
SEND_EXCHANGE: from_groupBy_ymp
ROUTES: #clave: valor | de donde viene : a donde va
  groupBy_ymp:
    - aggregate_top_seller_product
    - aggregate_top_profit_product
  # cluster_ymp:
  #   - cluster_ymp
---
OPERATOR_TYPE: year_month_user_store
QUEUE_LISTEN: to_groupBy_ymus_queue
LISTEN_EXCHANGE: from_filter_year
SEND_EXCHANGE: from_groupBy_ymus
END_EXCHANGE: ymus_end_exchange # cluster
ROUTES:
  groupBy_ymus:
    - aggregate_user
  # cluster_ymus:
  #   - cluster_ymus
---
OPERATOR_TYPE: year_semester_store
QUEUE_LISTEN: to_groupBy_yss_queue
LISTEN_EXCHANGE: from_filter_time
SEND_EXCHANGE: from_groupBy_yss
END_EXCHANGE: yss_end_exchange # cluster
ROUTES:
  groupBy_yss:
    - aggregate_tpv
  # cluster_yss:
  #   - cluster_yss