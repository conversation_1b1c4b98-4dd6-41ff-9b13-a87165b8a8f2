package logger

import (
	"os"

	"github.com/op/go-logging"
)

var Log *logging.Logger

var format = logging.MustStringFormatter(
	`%{color}%{time:2006-01-02 15:04:05} %{level:.4s} [%{module}] ▶ %{message}%{color:reset}`,
)

func Init(module string, level string) {
	Log = logging.MustGetLogger(module)

	backend := logging.NewLogBackend(os.Stdout, "", 0)
	format := logging.MustStringFormatter(
		`%{color}%{time:2006-01-02 15:04:05} %{level:.4s} [%{module}] ▶ %{message}%{color:reset}`,
	)

	backendFormat := logging.NewBackendFormatter(backend, format)
	leveled := logging.AddModuleLevel(backendFormat)

	logging.SetBackend(leveled)
}
