package main

import (
	"os"
	"tp1/internal/server/gateway"
	"tp1/middleware"

	"github.com/op/go-logging"
)

var log = logging.MustGetLogger("log")

func main() {
	middleware.Init()
	defer middleware.Close()
	serverAddress := os.Getenv("SERVER_ADDRESS")

	gateway, err := gateway.NewGateway(log)
	if err != nil {
		log.Errorf("action: init_gateway | status: error | err: %v", err)
		return
	}

	err = gateway.Start(serverAddress)
	if err != nil {
		log.Errorf("action: gateway | status: error | err: %v", err)
		return
	}
}
