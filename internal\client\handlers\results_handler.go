package handlers

import (
	"fmt"
	"log"
	"os"
	cs_protocol "tp1/pkg/protocol/client_server_protocol"
	t "tp1/pkg/protocol/table_types"
)

var RES_PATHS = map[string]t.TableType{
	"results_q1":   t.RESULTS_Q1,
	"results_q2_1": t.RESULTS_Q2_1,
	"results_q2_2": t.RESULTS_Q2_2,
	"results_q3":   t.RESULTS_Q3,
	"results_q4":   t.RESULTS_Q4,
}

type ResultsHandler struct {
	tableWriters map[t.TableType]*TableWriter
	basePath     string
}

func NewResultsHandler(basePath string) (*ResultsHandler, error) {
	tableWriters := make(map[t.TableType]*TableWriter)

	// Delete if it already exists
	if err := os.RemoveAll(basePath); err != nil {
		log.Fatalf("error deleting %s: %v", basePath, err)
	}

	// Make base path
	if err := os.MkdirAll(basePath, 0755); err != nil {
		return nil, fmt.Errorf("failed to create base directory %s: %w", basePath, err)
	}

	for resultPath, tableType := range RES_PATHS {
		fullPath := fmt.Sprintf("%s/%s", basePath, resultPath)
		if err := os.MkdirAll(fullPath, 0755); err != nil {
			return nil, fmt.Errorf("failed to create directory %s: %w", fullPath, err)
		}

		writer, err := NewTableWriter(fullPath, tableType)
		if err != nil {
			return nil, fmt.Errorf("failed to create table writer for %s: %w", fullPath, err)
		}
		tableWriters[tableType] = writer
	}

	return &ResultsHandler{
		tableWriters: tableWriters,
		basePath:     basePath,
	}, nil
}

func (rh *ResultsHandler) WriteBatch(msg *cs_protocol.BatchMessage) error {
	writer, exists := rh.tableWriters[msg.TableType]
	if !exists {
		return fmt.Errorf("no writer found for table type %d", msg.TableType)
	}
	if err := writer.WriteRows(&msg.Rows); err != nil {
		return fmt.Errorf("failed to write rows for table type %d: %w", msg.TableType, err)
	}

	return nil
}

func (rh *ResultsHandler) CloseWriters() {
	for _, writer := range rh.tableWriters {
		if err := writer.Close(); err != nil {
			fmt.Printf("error closing writer for table type %d: %v\n", writer.TableType, err)
		}
	}
}
