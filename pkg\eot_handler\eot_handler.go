package eot_handler

import (
	"tp1/pkg/errors"
	is_protocol "tp1/pkg/protocol/internal_server_protocol"
	"tp1/pkg/protocol/table_types"

	"github.com/op/go-logging"
)

type sendToNextNodeFunc func(formerRouteKeys []string, newMsg any, opCode is_protocol.ServerOpCode) error
type sendToClusterNodeFunc func(newMsg any, opCode is_protocol.ServerOpCode) error

type tableClient struct {
	tableType table_types.TableType
	clientID  string
}

type EOT<PERSON>and<PERSON> struct {
	registry          map[tableClient]int
	clusterSize       int
	sendToNextNode    sendToNextNodeFunc
	sendToClusterNode sendToClusterNodeFunc
	logger            *logging.Logger
}

func NewEOTHandler(clusterSize int, sendToNextNode sendToNextNodeFunc, sendToClusterNode sendToClusterNodeFunc, logger *logging.Logger) *EOTHandler {
	return &EOTHandler{
		registry:          make(map[tableClient]int),
		clusterSize:       clusterSize,
		sendToNextNode:    sendToNextNode,
		sendToClusterNode: sendToClusterNode,
		logger:            logger,
	}
}

func (e *EOTHandler) Handle(opCode is_protocol.ServerOpCode, msgBytes []byte) error {
	switch opCode {
	case is_protocol.END_OF_TABLE:
		return e.handleEndOfTableMessage(msgBytes)
	case is_protocol.EOT_RECEIVED:
		return e.handleEOTReceived(msgBytes)
	case is_protocol.EOT_ACK:
		return e.handleEOTAckMessage(msgBytes)
	default:
		return errors.UnkownServerOpCodeError
	}
}

// handleEndOfTableMessage handles End Of Table messages.
// If there is a single node in the cluster, it's his responisbility to forward the EOT message.
// If there are other nodes in the cluster, the EOT protocol begins -> the node forwards a ClusterEndOfTableMessage
// to the rest of the nodes in the cluster.
func (e *EOTHandler) handleEndOfTableMessage(msgBytes []byte) error {
	endMsg, err := is_protocol.NewServerMessage[is_protocol.EndOfTableMessage](msgBytes)
	if err != nil {
		return err
	}
	shouldForward := e.addEOT(&endMsg)
	if shouldForward { // Single Node - Forward instantly
		e.logger.Infof("Received EOT | Im single node, forwarding...")
		formerRouteKeys, err := is_protocol.GetRouteKeys(msgBytes)
		if err != nil {
			return err
		}
		return e.sendToNextNode(formerRouteKeys, endMsg, is_protocol.END_OF_TABLE)

	} else { // Multiple Nodes - Start EOT-Protocol
		e.logger.Infof("Received EOT | Broadcasting...")
		originalRouteKeys, err := is_protocol.GetRouteKeys(msgBytes)
		if err != nil {
			return err
		}
		clusterEndMsg := is_protocol.ClusterEndOfTableMessage{
			TableType:         endMsg.TableType,
			ClientId:          endMsg.ClientId,
			OriginalRouteKeys: originalRouteKeys,
		}
		return e.sendToClusterNode(clusterEndMsg, is_protocol.EOT_RECEIVED)
	}
}

// handleEOTReceived handles EOT Received messages.
// If the message is the one originally sent by this node, it ignores it.
// If not answers sending an ACK message
func (e *EOTHandler) handleEOTReceived(msgBytes []byte) error {
	clusterEndMsg, err := is_protocol.NewServerMessage[is_protocol.ClusterEndOfTableMessage](msgBytes)
	if err != nil {
		return err
	}

	if e.shouldIgnoreAckReceived(&clusterEndMsg) {
		return nil
	}
	e.logger.Infof("Received EOT from peer, sending ACK.")

	// need to send ack
	return e.sendToClusterNode(clusterEndMsg, is_protocol.EOT_ACK)
}

// handleEOTAckMessage handles AKC for ClusterEndOfTableMessage messages.
// Verifies if the AKC was supposed to be for another node (this node didn't start the protocol) and if all
// ACKs were received.
// Sends the original EOT to the next node in pipeline when corresponds.
func (e *EOTHandler) handleEOTAckMessage(msgBytes []byte) error {

	clusterEndMsg, err := is_protocol.NewServerMessage[is_protocol.ClusterEndOfTableMessage](msgBytes)
	if err != nil {
		return err
	}

	shouldSend := e.addEOTAck(&clusterEndMsg)
	if !shouldSend {
		return nil
	}
	e.logger.Infof("All ACKs received for %s-%s. Forwarding EOT.", clusterEndMsg.ClientId, clusterEndMsg.TableType.String())

	endMsg := is_protocol.EndOfTableMessage{
		TableType: clusterEndMsg.TableType,
		ClientId:  clusterEndMsg.ClientId,
	}
	return e.sendToNextNode(clusterEndMsg.OriginalRouteKeys, endMsg, is_protocol.END_OF_TABLE)
}

// AddEOT Returns true if the nodeis the only one, so the EOT can be forwarded already
func (e *EOTHandler) addEOT(endMsg *is_protocol.EndOfTableMessage) bool {
	if e.clusterSize == 1 {
		return true
	}
	tableClient := tableClient{tableType: endMsg.TableType, clientID: endMsg.ClientId}
	// if key already existed, is because I (this node instance) sent the EOT_RECEIVED, so ignore
	if e.registry[tableClient] != 0 {
		panic("should not happen, end of table registry should be 0 for a new eot")
	}
	e.registry[tableClient] = 1
	return false
}

func (e *EOTHandler) shouldIgnoreAckReceived(endMsg *is_protocol.ClusterEndOfTableMessage) bool {
	tableClient := tableClient{tableType: endMsg.TableType, clientID: endMsg.ClientId}
	// if key already existed, is because I (this node instance) sent the EOT_RECEIVED, so ignore
	return e.registry[tableClient] != 0
}

// AddEOTAck adds an Ack message to the registry.
// If all acks have been received, it returns true and removes the entry from the registry.
// If the ack is for an EOT that this node never sent or not all acks have been received yet, it returns false.
func (e *EOTHandler) addEOTAck(endMsg *is_protocol.ClusterEndOfTableMessage) bool {
	tableClient := tableClient{tableType: endMsg.TableType, clientID: endMsg.ClientId}
	// if key doesn't exist, is because I (this node instance) never sent the EOT_RECEIVED, ignore it
	if e.registry[tableClient] == 0 {
		return false
	}

	e.registry[tableClient]++
	if e.registry[tableClient] == e.clusterSize {
		delete(e.registry, tableClient)
		return true
	}

	return false
}
