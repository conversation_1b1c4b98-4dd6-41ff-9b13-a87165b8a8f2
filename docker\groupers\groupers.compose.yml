x-grouper-service: &grouper-service
  build:
    context: ../../
    dockerfile: docker/groupers/group_by.Dockerfile
  depends_on:
    rabbitmq:
      condition: service_healthy
  networks:
    - app-network
  env_file: ../../.env
  volumes:
    - ../../config/group_by_config.yaml:/group_by_config.yaml

services:
  group_by_year_month_product-0:
    <<: *grouper-service
    container_name: group_by_year_month_product-0
    environment:
      - GROUP_TYPE=year_month_product
      - CONFIG_PATH=/group_by_config.yaml
      - CLUSTER_SIZE=1
    depends_on:
      - filter-by-year-0
      # - filter-by-year-1
      # - filter-by-year-2

  group_by_year_month_user_store-0:
    <<: *grouper-service
    container_name: group_by_year_month_user_store-0
    environment:
      - GROUP_TYPE=year_month_user_store
      - CONFIG_PATH=/group_by_config.yaml
      - CLUSTER_SIZE=1
    depends_on:
      - filter-by-year-0
      # - filter-by-year-1
      # - filter-by-year-2

  # group_by_year_month_user_store-1:
  #   <<: *grouper-service
  #   container_name: group_by_year_month_user_store-1
  #   environment:
  #     - GROUP_TYPE=year_month_user_store
  #     - CONFIG_PATH=/group_by_config.yaml
  #     - CLUSTER_SIZE=2
  #   depends_on:
  #     - filter-by-year-0
  #     - filter-by-year-1
  #     - filter-by-year-2

  group_by_year_semester_store-0:
    <<: *grouper-service
    container_name: group_by_year_semester_store-0
    environment:
     - GROUP_TYPE=year_semester_store
     - CONFIG_PATH=/group_by_config.yaml
     - CLUSTER_SIZE=1
    depends_on:
      - filter-by-time-0
      # - filter-by-time-1

  # group_by_year_semester_store-1:
  #   <<: *grouper-service
  #   container_name: group_by_year_semester_store-1
  #   environment:
  #    - GROUP_TYPE=year_semester_store
  #    - CONFIG_PATH=/group_by_config.yaml
  #    - CLUSTER_SIZE=3
  #   depends_on:
  #     - filter-by-time-0
  #     - filter-by-time-1

  # group_by_year_semester_store-2:
  #   <<: *grouper-service
  #   container_name: group_by_year_semester_store-2
  #   environment:
  #    - GROUP_TYPE=year_semester_store
  #    - CONFIG_PATH=/group_by_config.yaml
  #    - CLUSTER_SIZE=3
  #   depends_on:
  #     - filter-by-time-0
  #     - filter-by-time-1
