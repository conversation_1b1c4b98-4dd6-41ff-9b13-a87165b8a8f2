package client_server_protocol

import (
	"bytes"
	"encoding/binary"
	"net"

	e "tp1/pkg/errors"
	"tp1/pkg/protocol/utils"
)

// SendMessage receives a Client-Server message and sends it once its serialized
func SendMessage[T any](conn net.Conn, msg T, opCode OpCode) error {
	msgBytes, err := utils.ToBytes(msg)
	if err != nil {
		return err
	}

	var buf bytes.Buffer

	// length prefix
	lengthPrefix := len(msgBytes) + OPCODE_LEN
	binary.Write(&buf, binary.BigEndian, uint32(lengthPrefix))

	// add opcode
	buf.WriteByte(byte(opCode))

	// payload
	buf.Write(msgBytes)

	fullWrite(conn, buf.Bytes())

	return nil
}

// ReadMessage Reads a message from the connection and deserializes it into a Client-Server message struct
func ReadMessage(conn net.Conn) (OpCode, []byte, error) {
	opCode, msgBytes, err := readWithLengthPrefix(conn)
	return OpCode(opCode), msgBytes, err
}

// fullWrite ensures all bytes are written to the connection, handling partial writes
func fullWrite(conn net.Conn, msg []byte) error { //TODO: does golang handle short writes
	for written := 0; written < len(msg); {
		n, err := conn.Write(msg[written:])
		if err != nil {
			return err
		}
		written += n
	}
	return nil
}

// fullRead Reads exactly size bytes from the connection, handling short-reads
func fullRead(conn net.Conn, size int) ([]byte, error) {
	buffer := make([]byte, size)
	totalRead := 0

	for totalRead < size {
		n, err := conn.Read(buffer[totalRead:])
		if err != nil {
			return nil, err
		}
		totalRead += n
	}

	return buffer, nil
}

// readWithLengthPrefix Reads a complete message using the payload length field
func readWithLengthPrefix(conn net.Conn) (uint8, []byte, error) {

	// 4 bytes, big-endian
	msgLengthBytes, err := fullRead(conn, LENGTH_PREFIX_LEN)
	if err != nil {
		return 0, nil, err
	}

	msgLength := parseUint32BigEndian(msgLengthBytes)

	if msgLength == 0 {
		return 0, nil, e.InvalidMessageLenError
	}

	msgWithOpcode, err := fullRead(conn, int(msgLength))
	if err != nil {
		return 0, nil, err
	}

	if len(msgWithOpcode) == 0 {
		return 0, nil, e.EmptyMessageError
	}

	return msgWithOpcode[OPCODE_INDEX], msgWithOpcode[PAYLOAD_INDEX:], nil
}

// parseUint32BigEndian converts a 4-byte slice to uint32 using big-endian byte order
func parseUint32BigEndian(data []byte) uint32 {
	if len(data) < LENGTH_PREFIX_LEN {
		return 0
	}
	return uint32(data[0])<<24 |
		uint32(data[1])<<16 |
		uint32(data[2])<<8 |
		uint32(data[3])
}
