package errors

type InternalServerProtocolError string

func (pe InternalServerProtocolError) Error() string {
	return string(pe)
}

// Defines possible erros for server protocol
const (
	RouteKeysTooLongError     InternalServerProtocolError = "Route keys length exceeds maximum allowed"
	InvalidRouteKeyError      InternalServerProtocolError = "Route key contains invalid character"
	MissingRouteKeysError     InternalServerProtocolError = "Route keys are missing"
	MessageNotLongEnoughError InternalServerProtocolError = "Message is not long enough to contain headers"
)
